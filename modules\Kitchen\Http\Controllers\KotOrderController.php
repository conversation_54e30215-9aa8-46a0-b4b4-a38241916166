<?php

namespace Modules\Kitchen\Http\Controllers;

use App\Http\Controllers\Controller;
use Modules\Kitchen\Models\KotOrder;
use Modules\Kitchen\Models\KotOrderItem;
use Modules\Kitchen\Models\Kitchen;
use Modules\Kitchen\Services\KotService;
use Modules\Kitchen\Http\Requests\UpdateKotStatusRequest;
use Modules\Kitchen\Http\Requests\UpdateKotPriorityRequest;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\View\View;
use Yajra\DataTables\Facades\DataTables;

class KotOrderController extends Controller
{
    public function __construct(
        private KotService $kotService
    ) {}

    /**
     * Display a listing of KOT orders.
     */
    public function index(Request $request): View|JsonResponse
    {
        if ($request->ajax()) {
            $kotOrders = KotOrder::with(['kitchen', 'order', 'assignedTo', 'kotOrderItems.menuItem'])
                 ->where('status', '!=', 'COMPLETED')
            ->select([
                    'id', 'kitchen_id', 'order_id', 'kot_number', 'status', 'priority',
                    'estimated_prep_time_minutes', 'actual_prep_time_minutes',
                    'assigned_to', 'created_at', 'started_at', 'completed_at', 'branch_id'
                ]);

            // Apply branch filter for current user
            $user = auth()->user();
            if ($user && $user->branch_id && !$user->hasRole('admin')) {
                $kotOrders->where('branch_id', $user->branch_id);
            } elseif ($request->filled('branch_id')) {
                $kotOrders->where('branch_id', $request->branch_id);
            }

            // Apply filters
            if ($request->filled('kitchen_id')) {
                $kotOrders->where('kitchen_id', $request->kitchen_id);
            }

            if ($request->filled('status')) {
                $kotOrders->where('status', $request->status);
            }

            if ($request->filled('priority')) {
                $kotOrders->where('priority', $request->priority);
            }

            // Handle card view request
            if ($request->get('view') === 'cards') {
                $kotOrdersData = $kotOrders->get()->map(function ($kotOrder) {
                    $orderNumber = $kotOrder->order?->order_number ?? 'N/A';
                    $shortOrderNumber = strlen($orderNumber) > 15 ? 
                        substr($orderNumber, 0, 8) . '...' . substr($orderNumber, -4) : 
                        $orderNumber;
                    
                    return [
                        'id' => $kotOrder->id,
                        'kot_number' => $kotOrder->kot_number,
                        'status' => $kotOrder->status,
                        'priority' => $kotOrder->priority,
                        'kitchen_name' => $kotOrder->kitchen?->name ?? 'N/A',
                        'order_number' => $orderNumber,
                        'short_order_number' => $shortOrderNumber,
                        'assigned_to_name' => $kotOrder->assignedTo?->name ?? 'Unassigned',
                        'elapsed_time' => $kotOrder->status === 'completed' 
                            ? ($kotOrder->actual_prep_time_minutes ? $kotOrder->actual_prep_time_minutes . ' min' : 'N/A')
                            : $kotOrder->getElapsedTimeMinutes() . ' min',
                        'remaining_time' => $kotOrder->status === 'completed' 
                            ? 'Completed' 
                            : ($kotOrder->getRemainingTimeMinutes() !== null ? $kotOrder->getRemainingTimeMinutes() . ' min' : 'N/A'),
                        'created_at' => $kotOrder->created_at->format('M d, Y H:i'),
                        'items' => $kotOrder->kotOrderItems->map(function ($item) {
                            return [
                                'id' => $item->id,
                                'menu_item_name' => $item->menuItem?->name ?? 'Unknown Item',
                                'quantity' => $item->quantity,
                                'status' => $item->status,
                                'special_instructions' => $item->special_instructions,
                                'modifications' => $item->modifications,
                            ];
                        }),
                        'items_count' => $kotOrder->kotOrderItems->count(),
                        'pending_items' => $kotOrder->kotOrderItems->where('status', 'pending')->count(),
                        'preparing_items' => $kotOrder->kotOrderItems->where('status', 'preparing')->count(),
                        'ready_items' => $kotOrder->kotOrderItems->where('status', 'ready')->count(),
                        'completed_items' => $kotOrder->kotOrderItems->where('status', 'completed')->count(),
                        'items_summary' => [
                            'pending' => $kotOrder->kotOrderItems->where('status', 'pending')->count(),
                            'preparing' => $kotOrder->kotOrderItems->where('status', 'preparing')->count(),
                            'ready' => $kotOrder->kotOrderItems->where('status', 'ready')->count(),
                            'completed' => $kotOrder->kotOrderItems->where('status', 'completed')->count(),
                        ],
                    ];
                });

                return response()->json([
                    'success' => true,
                    'data' => $kotOrdersData
                ]);
            }

            return DataTables::of($kotOrders)
                ->addIndexColumn()
            ->addColumn('kitchen_name', function ($kotOrder) {
                    return $kotOrder->kitchen?->name ?? 'N/A';
                })
                ->addColumn('order_number', function ($kotOrder) {
                    return $kotOrder->order?->order_number ?? 'N/A';
                })
                ->addColumn('assigned_to_name', function ($kotOrder) {
                    return $kotOrder->assignedTo?->name ?? 'Unassigned';
                })
                ->addColumn('status_badge', function ($kotOrder) {
                    $statusClasses = [
                        'pending' => 'warning',
                        'preparing' => 'info',
                        'ready' => 'success',
                        'completed' => 'primary',
                        'cancelled' => 'danger',
                    ];
                    $statusClass = $statusClasses[$kotOrder->status] ?? 'secondary';
                    return "<span class='badge badge-{$statusClass}'>" . ucfirst($kotOrder->status) . "</span>";
                })
                ->addColumn('priority_badge', function ($kotOrder) {
                    $priorityClasses = [
                        'low' => 'secondary',
                        'normal' => 'primary',
                        'high' => 'warning',
                        'urgent' => 'danger',
                    ];
                    $priorityClass = $priorityClasses[$kotOrder->priority] ?? 'secondary';
                    return "<span class='badge badge-{$priorityClass}'>" . ucfirst($kotOrder->priority) . "</span>";
                })
                ->addColumn('elapsed_time', function ($kotOrder) {
                    if ($kotOrder->status === 'completed') {
                        return $kotOrder->actual_prep_time_minutes ? 
                            $kotOrder->actual_prep_time_minutes . ' min' : 'N/A';
                    }
                    return $kotOrder->getElapsedTimeMinutes() . ' min';
                })
                ->addColumn('remaining_time', function ($kotOrder) {
                    if ($kotOrder->status === 'completed') {
                        return 'Completed';
                    }
                    
                    $remaining = $kotOrder->getRemainingTimeMinutes();
                    if ($remaining === null) {
                        return 'N/A';
                    }
                    
                    $class = $remaining <= 0 ? 'text-danger' : ($remaining <= 5 ? 'text-warning' : 'text-success');
                    return "<span class='{$class}'>{$remaining} min</span>";
                })
                ->addColumn('actions', function ($kotOrder) {
                    return view('kitchen::partials.kot-actions', compact('kotOrder'))->render();
                })
                ->editColumn('created_at', function ($kotOrder) {
                    return $kotOrder->created_at->format('M d, Y H:i');
                })
                ->rawColumns(['status_badge', 'priority_badge', 'remaining_time', 'actions'])
                ->make(true);
        }

        // Get kitchens based on user's branch
        $user = auth()->user();
        $kitchensQuery = Kitchen::active();
        
        if ($user && $user->branch_id && !$user->hasRole('admin')) {
            $kitchensQuery->where('branch_id', $user->branch_id);
        }
        
        $kitchens = $kitchensQuery->get();
        
        // Get branches for admin users
        $branches = collect();
        if ($user && $user->hasRole('admin')) {
            $branches = \App\Models\Branch::active()->get();
        }
        
        $statuses = ['pending', 'preparing', 'ready', 'completed', 'cancelled'];
        $priorities = ['low', 'normal', 'high', 'urgent'];

        return view('kitchen::kot-orders.index', compact('kitchens', 'statuses', 'priorities', 'branches'));
    }

    /**
     * Display the specified KOT order.
     */
    public function show(KotOrder $kotOrder): View
    {
        $kotOrder->load([
            'kitchen',
            'order.customer',
            'assignedTo',
            'creator',
            'kotOrderItems.menuItem.category',
            'kotOrderItems.orderItem'
        ]);

        return view('kitchen::kot-orders.show', compact('kotOrder'));
    }

    /**
     * Update KOT status.
     */
    public function updateStatus(UpdateKotStatusRequest $request, KotOrder $kotOrder): JsonResponse
    {
        try {
            $status = $request->status;
            $userId = $request->assigned_to;

            switch ($status) {
                case 'preparing':
                    $kotOrder = $this->kotService->startKot($kotOrder, $userId);
                    break;
                case 'ready':
                    $kotOrder = $this->kotService->markKotReady($kotOrder);
                    break;
                case 'completed':
                    $kotOrder = $this->kotService->completeKot($kotOrder);
                    break;
                case 'cancelled':
                    $kotOrder = $this->kotService->cancelKot($kotOrder, $request->reason);
                    break;
                default:
                    throw new \Exception('Invalid status transition');
            }

            return response()->json([
                'success' => true,
                'message' => 'KOT status updated successfully.',
                'data' => $kotOrder
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to update KOT status: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Update KOT priority.
     */
    public function updatePriority(UpdateKotPriorityRequest $request, KotOrder $kotOrder): JsonResponse
    {
        try {
            $kotOrder = $this->kotService->updateKotPriority($kotOrder, $request->priority);

            return response()->json([
                'success' => true,
                'message' => 'KOT priority updated successfully.',
                'data' => $kotOrder
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to update KOT priority: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Assign KOT to user.
     */
    public function assign(Request $request, KotOrder $kotOrder): JsonResponse
    {
        $request->validate([
            'user_id' => 'required|integer|exists:users,id'
        ]);

        try {
            $kotOrder = $this->kotService->assignKot($kotOrder, $request->user_id);

            return response()->json([
                'success' => true,
                'message' => 'KOT assigned successfully.',
                'data' => $kotOrder
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to assign KOT: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Kitchen display view for active KOTs.
     */
    public function kitchenDisplay(Request $request, Kitchen $kitchen = null): View
    {
        $kitchenId = $kitchen?->id ?? $request->kitchen_id;
        $activeKots = $this->kotService->getActiveKotsForDisplay($kitchenId);
        $kitchens = Kitchen::active()->get();

        return view('kitchen::kot-orders.kitchen-display', compact('activeKots', 'kitchens', 'kitchen'));
    }

    /**
     * Get KOT statistics for dashboard.
     */
    public function statistics(Request $request): JsonResponse
    {
        $kitchenId = $request->kitchen_id;
        $statistics = $this->kotService->getKotStatistics($kitchenId);

        return response()->json([
            'success' => true,
            'data' => $statistics
        ]);
    }

    /**
     * Print KOT.
     */
    public function print(KotOrder $kotOrder): View
    {
        $kotOrder->load([
            'kitchen',
            'order.customer',
            'kotOrderItems.menuItem.category'
        ]);

        return view('kitchen::kot-orders.print', compact('kotOrder'));
    }

    /**
     * Get active KOTs for real-time updates.
     */
    public function activeKots(Request $request): JsonResponse
    {
        $kitchenId = $request->kitchen_id;
        $activeKots = $this->kotService->getActiveKotsForDisplay($kitchenId);

        return response()->json([
            'success' => true,
            'data' => $activeKots->map(function ($kot) {
                return [
                    'id' => $kot->id,
                    'kot_number' => $kot->kot_number,
                    'status' => $kot->status,
                    'priority' => $kot->priority,
                    'order_number' => $kot->order?->order_number,
                    'elapsed_time' => $kot->getElapsedTimeMinutes(),
                    'remaining_time' => $kot->getRemainingTimeMinutes(),
                    'is_overdue' => $kot->isOverdue(),
                    'items_count' => $kot->kotOrderItems->count(),
                    'created_at' => $kot->created_at->format('H:i'),
                ];
            })
        ]);
    }

    /**
     * Update individual KOT item status.
     */
    public function updateItemStatus(Request $request, KotOrderItem $kotOrderItem): JsonResponse
    {
        $request->validate([
            'status' => 'required|in:pending,preparing,ready,completed,cancelled'
        ]);

        try {
            $status = $request->status;
            
            switch ($status) {
                case 'preparing':
                    $kotOrderItem->start();
                    break;
                case 'ready':
                    $kotOrderItem->markReady();
                    break;
                case 'completed':
                    $kotOrderItem->complete();
                    break;
                case 'cancelled':
                    $kotOrderItem->cancel($request->reason);
                    break;
                default:
                    $kotOrderItem->update(['status' => $status]);
            }

            // Check if all items are completed to update KOT status
            $kotOrder = $kotOrderItem->kotOrder;
            $allItemsCompleted = $kotOrder->kotOrderItems()->where('status', '!=', 'completed')->count() === 0;
            
            if ($allItemsCompleted && $kotOrder->status !== 'completed') {
                $kotOrder->update(['status' => 'ready']);
            }

            return response()->json([
                'success' => true,
                'message' => 'KOT item status updated successfully.',
                'data' => $kotOrderItem->fresh()
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to update KOT item status: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Mark all KOT items as ready.
     */
    public function markAllItemsReady(KotOrder $kotOrder): JsonResponse
    {
        try {
            $kotOrder->kotOrderItems()->whereIn('status', ['pending', 'preparing'])
                ->update(['status' => 'ready']);
            
            $kotOrder->update(['status' => 'ready']);

            return response()->json([
                'success' => true,
                'message' => 'All KOT items marked as ready.',
                'data' => $kotOrder->fresh()
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to mark items as ready: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Mark all KOT items as completed.
     */
    public function markAllItemsCompleted(KotOrder $kotOrder): JsonResponse
    {
        try {
            $kotOrder->kotOrderItems()->whereIn('status', ['pending', 'preparing', 'ready'])
                ->update([
                    'status' => 'completed',
                    'completed_at' => now()
                ]);
            
            $kotOrder->update([
                'status' => 'completed',
                'completed_at' => now()
            ]);

            return response()->json([
                'success' => true,
                'message' => 'All KOT items marked as completed.',
                'data' => $kotOrder->fresh()
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to mark items as completed: ' . $e->getMessage()
            ], 500);
        }
    }
}
