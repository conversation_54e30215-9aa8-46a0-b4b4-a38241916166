<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('payments', function (Blueprint $table) {
            $table->foreignId('branch_id')->after('tenant_id')->constrained('branches')->onDelete('cascade');
            
            // Add index for better performance
            $table->index(['tenant_id', 'branch_id', 'status']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('payments', function (Blueprint $table) {
            $table->dropForeign(['branch_id']);
            $table->dropIndex(['tenant_id', 'branch_id', 'status']);
            $table->dropColumn('branch_id');
        });
    }
};
