@extends('layouts.master')
@push('styles')
<meta name="csrf-token" content="{{ csrf_token() }}">
<!-- DataTables CSS -->
<link rel="stylesheet" href="https://cdn.datatables.net/1.13.7/css/jquery.dataTables.min.css">
<link rel="stylesheet" href="https://cdn.datatables.net/buttons/2.4.2/css/buttons.dataTables.min.css">
<link rel="stylesheet" href="https://cdn.datatables.net/responsive/2.5.0/css/responsive.dataTables.min.css">
<!-- Select2 CSS -->
<link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet" />
<style>
    /* Custom DataTables Tailwind Styling */
    .dataTables_wrapper {
        font-family: inherit;
    }
    
    .dataTables_wrapper .dataTables_length,
    .dataTables_wrapper .dataTables_filter,
    .dataTables_wrapper .dataTables_info,
    .dataTables_wrapper .dataTables_processing,
    .dataTables_wrapper .dataTables_paginate {
        color: #374151;
        font-size: 0.875rem;
        padding: 0.5rem 0.75rem;
    }
    .dataTables_processing{
        margin: auto
    }
    .dataTables_wrapper .dataTables_length select,
    .dataTables_wrapper .dataTables_filter input {
        border: 1px solid #d1d5db;
        border-radius: 0.375rem;
        padding: 0.375rem 0.75rem;
        font-size: 0.875rem;
    }
    
    .dataTables_wrapper .dataTables_filter input:focus {
        outline: none;
        border-color: #3b82f6;
        box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
    }
    
    .dataTables_wrapper .dataTables_paginate .paginate_button {
        padding: 0.5rem 0.75rem !important;
        margin: 0 0.125rem !important;
        border-radius: 0.375rem !important;
        border: 1px solid #d1d5db !important;
        background: white !important;
        color: #374151 !important;
        text-decoration: none !important;
    }
    
    .dataTables_wrapper .dataTables_paginate .paginate_button:hover {
        background: #f3f4f6 !important;
        border-color: #9ca3af !important;
        color: #374151 !important;
    }
    
    .dataTables_wrapper .dataTables_paginate .paginate_button.current {
        background: #3b82f6 !important;
        border-color: #3b82f6 !important;
        color: white !important;
    }
    
    .dataTables_wrapper .dataTables_paginate .paginate_button.disabled {
        background: #f9fafb !important;
        color: #9ca3af !important;
        border-color: #e5e7eb !important;
    }
    
    table.dataTable {
        border-collapse: separate !important;
        border-spacing: 0 !important;
    }
    
    table.dataTable thead th {
        background-color: #f9fafb;
        border-bottom: 1px solid #e5e7eb;
        font-weight: 600;
        color: #374151;
        padding: 0.75rem;
    }
    
    table.dataTable tbody td {
        padding: 0.75rem;
        border-bottom: 1px solid #f3f4f6;
    }
    
    table.dataTable tbody tr:hover {
        background-color: #f9fafb;
    }
    
    .select2-container--default .select2-selection--single {
        height: 42px;
        border: 1px solid #d1d5db;
        border-radius: 0.5rem;
        padding: 0.5rem;
    }
    
    .select2-container--default .select2-selection--single .select2-selection__rendered {
        line-height: 26px;
        padding-left: 0;
    }
    
    .select2-container--default .select2-selection--single .select2-selection__arrow {
        height: 40px;
    }
    
    .modal-backdrop {
        background-color: rgba(0, 0, 0, 0.5);
    }
    
    .animate-fadeIn {
        animation: fadeIn 0.3s ease-in-out;
    }
    
    @keyframes fadeIn {
        from { opacity: 0; transform: scale(0.95); }
        to { opacity: 1; transform: scale(1); }
    }
    
    .animate-slideDown {
        animation: slideDown 0.3s ease-in-out;
    }
    
    @keyframes slideDown {
        from { opacity: 0; transform: translateY(-10px); }
        to { opacity: 1; transform: translateY(0); }
    }
</style>
@endpush

@section('content')
<!-- Page Header -->
<div class="mb-6">
    <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between">
        <div class="mb-4 sm:mb-0">
            <nav class="flex mb-2" aria-label="Breadcrumb">
                <ol class="inline-flex items-center space-x-1 md:space-x-3">
                    <li class="inline-flex items-center">
                        <span class="text-gray-700 text-sm font-medium">إدارة النظام</span>
                    </li>
                    <li>
                        <div class="flex items-center">
                            <i class="fas fa-chevron-left text-gray-400 mx-2"></i>
                            <span class="text-gray-500 text-sm">الأدوار</span>
                        </div>
                    </li>
                </ol>
            </nav>
            <h1 class="text-2xl font-bold text-gray-900 flex items-center">
                <i class="fas fa-user-tag mr-3"></i>
                إدارة الأدوار
            </h1>
            <p class="text-sm text-gray-600 mt-1">إدارة أدوار النظام وصلاحياتها</p>
        </div>
        <div>
            <button type="button" class="inline-flex items-center px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white text-sm font-medium rounded-md transition-colors duration-200" onclick="openModal('addRoleModal')">
                <i class="fas fa-plus mr-2"></i>
                إضافة دور جديد
            </button>
        </div>
    </div>
</div>

<!-- Main Content -->
<div class="bg-white rounded-lg shadow-sm border border-gray-200">
    <div class="px-6 py-4 border-b border-gray-200">
        <div class="flex justify-between items-center">
            <div>
                <h3 class="text-lg font-medium text-gray-900">قائمة الأدوار</h3>
                <p class="text-sm text-gray-600 mt-1">إدارة أدوار النظام وصلاحياتها</p>
            </div>
        </div>
    </div>
    <div class="p-6">
        <!-- DataTable Controls -->
        <div class="mb-6 flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
            <div class="flex flex-col sm:flex-row gap-4">
                <!-- Search -->
                <div class="relative">
                    <div class="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                        <i class="fas fa-search text-gray-400"></i>
                    </div>
                    <input type="text" id="globalSearch" placeholder="البحث في الأدوار..." class="w-full sm:w-64 pl-3 pr-10 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                </div>

                <!-- Guard Filter -->
                <select id="guardFilter" class="px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                    <option value="">جميع الحراس</option>
                    <option value="web">ويب</option>
                    <option value="api">API</option>
                    <option value="sanctum">Sanctum</option>
                </select>
            </div>

            <!-- Export Buttons -->
            <div class="flex gap-2">
                <button type="button" id="exportExcel" class="px-3 py-2 bg-green-600 text-white text-sm font-medium rounded-md hover:bg-green-700 flex items-center gap-2">
                    <i class="fas fa-file-excel"></i>
                    Excel
                </button>
                <button type="button" id="exportPdf" class="px-3 py-2 bg-red-600 text-white text-sm font-medium rounded-md hover:bg-red-700 flex items-center gap-2">
                    <i class="fas fa-file-pdf"></i>
                    PDF
                </button>
                <button type="button" id="printTable" class="px-3 py-2 bg-gray-600 text-white text-sm font-medium rounded-md hover:bg-gray-700 flex items-center gap-2">
                    <i class="fas fa-print"></i>
                    طباعة
                </button>
            </div>
        </div>

        <!-- DataTable Container -->
        <div class="bg-white shadow-sm rounded-lg border border-gray-200 overflow-hidden">
            <div class="overflow-x-auto">
                <table id="rolesTable" class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th class="px-6 py-4 text-right text-xs font-medium text-gray-500 uppercase tracking-wider border-b border-gray-200">#</th>
                            <th class="px-6 py-4 text-right text-xs font-medium text-gray-500 uppercase tracking-wider border-b border-gray-200">اسم الدور</th>
                            <th class="px-6 py-4 text-right text-xs font-medium text-gray-500 uppercase tracking-wider border-b border-gray-200">الحارس</th>
                            <th class="px-6 py-4 text-right text-xs font-medium text-gray-500 uppercase tracking-wider border-b border-gray-200">عدد الصلاحيات</th>
                            <th class="px-6 py-4 text-right text-xs font-medium text-gray-500 uppercase tracking-wider border-b border-gray-200">عدد المستخدمين</th>
                            <th class="px-6 py-4 text-right text-xs font-medium text-gray-500 uppercase tracking-wider border-b border-gray-200">تاريخ الإنشاء</th>
                            <th class="px-6 py-4 text-right text-xs font-medium text-gray-500 uppercase tracking-wider border-b border-gray-200">الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                    </tbody>
                </table>
            </div>

            <!-- DataTable Info and Pagination -->
            <div class="bg-gray-50 px-6 py-3 border-t border-gray-200">
                <div class="flex items-center justify-between">
                    <div class="text-sm text-gray-700" id="tableInfo">
                        <!-- DataTable info will be inserted here -->
                    </div>
                    <div id="tablePagination">
                        <!-- DataTable pagination will be inserted here -->
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Add Role Modal -->
<div id="addRoleModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden z-50">
    <div class="relative top-10 mx-auto p-0 border-0 w-11/12 max-w-4xl shadow-2xl rounded-lg bg-white">
        <!-- Modal Header -->
        <div class="flex justify-between items-center px-6 py-4 bg-gradient-to-r from-blue-600 to-blue-700 text-white rounded-t-lg">
            <div class="flex items-center gap-3">
                <div class="w-8 h-8 bg-white bg-opacity-20 rounded-full flex items-center justify-center">
                    <i class="fas fa-user-tag text-sm"></i>
                </div>
                <h3 class="text-lg font-semibold">إضافة دور جديد</h3>
            </div>
            <button type="button" class="text-white hover:text-gray-200 transition-colors duration-200" onclick="closeModal('addRoleModal')">
                <i class="fas fa-times text-lg"></i>
            </button>
        </div>
        <!-- Modal Body -->
        <div class="px-6 py-6 max-h-96 overflow-y-auto">
            <form id="addRoleForm" action="{{ route('roles.store') }}" method="POST">
                @csrf
                <div class="space-y-6">
                    <!-- Basic Information -->
                    <div class="bg-gray-50 p-4 rounded-lg">
                        <h4 class="text-sm font-medium text-gray-900 mb-4 flex items-center gap-2">
                            <i class="fas fa-info-circle text-blue-600"></i>
                            المعلومات الأساسية
                        </h4>
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div>
                                <label for="name" class="block text-sm font-medium text-gray-700 mb-2">اسم الدور <span class="text-red-500">*</span></label>
                                <div class="relative">
                                    <div class="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                                        <i class="fas fa-tag text-gray-400 text-sm"></i>
                                    </div>
                                    <input type="text" class="w-full pl-3 pr-10 py-3 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors duration-200" id="name" name="name" required placeholder="أدخل اسم الدور">
                                </div>
                            </div>
                            <div>
                                <label for="guard_name" class="block text-sm font-medium text-gray-700 mb-2">الحارس <span class="text-red-500">*</span></label>
                                <div class="relative">
                                    <div class="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                                        <i class="fas fa-shield-alt text-gray-400 text-sm"></i>
                                    </div>
                                    <select class="w-full pl-3 pr-10 py-3 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors duration-200" id="guard_name" name="guard_name" required>
                                        <option value="web">ويب</option>
                                        <option value="api">API</option>
                                        <option value="sanctum">Sanctum</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Permissions Section -->
                    <div class="bg-gray-50 p-4 rounded-lg">
                        <h4 class="text-sm font-medium text-gray-900 mb-4 flex items-center gap-2">
                            <i class="fas fa-key text-blue-600"></i>
                            الصلاحيات
                        </h4>
                        <div class="permissions-container max-h-80 overflow-y-auto border border-gray-200 rounded-md p-4 bg-white">
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                @foreach($permissions as $group => $groupPermissions)
                                    <div class="bg-gray-50 rounded-lg p-3">
                                        <div class="flex items-center mb-3 pb-2 border-b border-gray-200">
                                            <input type="checkbox" class="group-checkbox h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded" data-group="{{ $group }}">
                                            <label class="mr-2 text-sm font-medium text-gray-900">{{ ucfirst($group) }}</label>
                                        </div>
                                        <div class="space-y-2">
                                            @foreach($groupPermissions as $permission)
                                                <div class="flex items-center">
                                                    <input class="permission-checkbox h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded" type="checkbox"
                                                           name="permissions[]" value="{{ $permission->name }}"
                                                           id="perm_{{ $permission->id }}" data-group="{{ $group }}">
                                                    <label class="mr-2 text-sm text-gray-700" for="perm_{{ $permission->id }}">
                                                        {{ $permission->name }}
                                                    </label>
                                                </div>
                                            @endforeach
                                        </div>
                                    </div>
                                @endforeach
                            </div>
                        </div>
                    </div>
                </div>
            </form>
        </div>

        <!-- Modal Footer -->
        <div class="px-6 py-4 bg-gray-50 border-t border-gray-200 rounded-b-lg">
            <div class="flex justify-end space-x-3">
                <button type="button" class="px-6 py-2 bg-gray-500 text-white text-sm font-medium rounded-lg hover:bg-gray-600 transition-colors duration-200 flex items-center gap-2" onclick="closeModal('addRoleModal')">
                    <i class="fas fa-times"></i>
                    إلغاء
                </button>
                <button type="submit" form="addRoleForm" class="px-6 py-2 bg-blue-600 text-white text-sm font-medium rounded-lg hover:bg-blue-700 transition-colors duration-200 flex items-center gap-2">
                    <i class="fas fa-save"></i>
                    حفظ الدور
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Edit Role Modal -->
<div id="editRoleModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden z-50">
    <div class="relative top-10 mx-auto p-0 border-0 w-11/12 max-w-4xl shadow-2xl rounded-lg bg-white">
        <!-- Modal Header -->
        <div class="flex justify-between items-center px-6 py-4 bg-gradient-to-r from-indigo-600 to-indigo-700 text-white rounded-t-lg">
            <div class="flex items-center gap-3">
                <div class="w-8 h-8 bg-white bg-opacity-20 rounded-full flex items-center justify-center">
                    <i class="fas fa-edit text-sm"></i>
                </div>
                <h3 class="text-lg font-semibold">تعديل الدور</h3>
            </div>
            <button type="button" class="text-white hover:text-gray-200 transition-colors duration-200" onclick="closeModal('editRoleModal')">
                <i class="fas fa-times text-lg"></i>
            </button>
        </div>
        <!-- Modal Body -->
        <div class="px-6 py-6 max-h-96 overflow-y-auto">
            <form id="editRoleForm" method="POST">
                @csrf
                @method('PUT')
                <div class="space-y-6">
                    <!-- Basic Information -->
                    <div class="bg-gray-50 p-4 rounded-lg">
                        <h4 class="text-sm font-medium text-gray-900 mb-4 flex items-center gap-2">
                            <i class="fas fa-info-circle text-indigo-600"></i>
                            المعلومات الأساسية
                        </h4>
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div>
                                <label for="edit_name" class="block text-sm font-medium text-gray-700 mb-2">اسم الدور <span class="text-red-500">*</span></label>
                                <div class="relative">
                                    <div class="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                                        <i class="fas fa-tag text-gray-400 text-sm"></i>
                                    </div>
                                    <input type="text" class="w-full pl-3 pr-10 py-3 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 transition-colors duration-200" id="edit_name" name="name" required>
                                </div>
                            </div>
                            <div>
                                <label for="edit_guard_name" class="block text-sm font-medium text-gray-700 mb-2">الحارس <span class="text-red-500">*</span></label>
                                <div class="relative">
                                    <div class="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                                        <i class="fas fa-shield-alt text-gray-400 text-sm"></i>
                                    </div>
                                    <select class="w-full pl-3 pr-10 py-3 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 transition-colors duration-200" id="edit_guard_name" name="guard_name" required>
                                        <option value="web">ويب</option>
                                        <option value="api">API</option>
                                        <option value="sanctum">Sanctum</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Permissions Section -->
                    <div class="bg-gray-50 p-4 rounded-lg">
                        <h4 class="text-sm font-medium text-gray-900 mb-4 flex items-center gap-2">
                            <i class="fas fa-key text-indigo-600"></i>
                            الصلاحيات
                        </h4>
                        <div class="edit-permissions-container max-h-80 overflow-y-auto border border-gray-200 rounded-md p-4 bg-white">
                            <!-- Permissions will be loaded here -->
                        </div>
                    </div>
                </div>
            </form>
        </div>

        <!-- Modal Footer -->
        <div class="px-6 py-4 bg-gray-50 border-t border-gray-200 rounded-b-lg">
            <div class="flex justify-end space-x-3">
                <button type="button" class="px-6 py-2 bg-gray-500 text-white text-sm font-medium rounded-lg hover:bg-gray-600 transition-colors duration-200 flex items-center gap-2" onclick="closeModal('editRoleModal')">
                    <i class="fas fa-times"></i>
                    إلغاء
                </button>
                <button type="submit" form="editRoleForm" class="px-6 py-2 bg-indigo-600 text-white text-sm font-medium rounded-lg hover:bg-indigo-700 transition-colors duration-200 flex items-center gap-2">
                    <i class="fas fa-save"></i>
                    تحديث الدور
                </button>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<!-- DataTables JS -->
<script src="https://cdn.datatables.net/1.13.7/js/jquery.dataTables.min.js"></script>
<script src="https://cdn.datatables.net/buttons/2.4.2/js/dataTables.buttons.min.js"></script>
<script src="https://cdn.datatables.net/buttons/2.4.2/js/buttons.html5.min.js"></script>
<script src="https://cdn.datatables.net/buttons/2.4.2/js/buttons.print.min.js"></script>
<script src="https://cdn.datatables.net/responsive/2.5.0/js/dataTables.responsive.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/jszip/3.10.1/jszip.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/pdfmake/0.1.53/pdfmake.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/pdfmake/0.1.53/vfs_fonts.js"></script>
<!-- Select2 JS -->
<script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>
<!-- SweetAlert -->
<script src="https://unpkg.com/sweetalert/dist/sweetalert.min.js"></script>

<script>
$(document).ready(function() {
    // Initialize DataTable
    var table = $('#rolesTable').DataTable({
        processing: true,
        serverSide: true,
        ajax: {
            url: "{{ route('roles.data') }}",
            data: function (d) {
                d.guard_name = $('#guardFilter').val();
            }
        },
        columns: [
            {data: 'DT_RowIndex', name: 'DT_RowIndex', orderable: false, searchable: false},
            {data: 'name', name: 'name'},
            {data: 'guard_badge', name: 'guard_name'},
            {data: 'permissions_count', name: 'permissions_count', orderable: false, searchable: false},
            {data: 'users_count', name: 'users_count', orderable: false, searchable: false},
            {data: 'created_at', name: 'created_at'},
            {data: 'action', name: 'action', orderable: false, searchable: false}
        ],
        dom: 'Bfrtip',
        buttons: [
            {
                extend: 'excel',
                text: 'Excel',
                className: 'hidden'
            },
            {
                extend: 'pdf',
                text: 'PDF',
                className: 'hidden'
            },
            {
                extend: 'print',
                text: 'Print',
                className: 'hidden'
            }
        ],
        responsive: true,
        language: {
            url: 'https://cdn.datatables.net/plug-ins/1.13.7/i18n/ar.json'
        },
        order: [[1, 'asc']],
        pageLength: 25,
        lengthMenu: [[10, 25, 50, 100, -1], [10, 25, 50, 100, "الكل"]]
    });

    // Global search
    $('#globalSearch').on('keyup', function() {
        table.search(this.value).draw();
    });

    // Guard filter
    $('#guardFilter').on('change', function() {
        table.draw();
    });

    // Export buttons
    $('#exportExcel').on('click', function() {
        table.button('.buttons-excel').trigger();
    });

    $('#exportPdf').on('click', function() {
        table.button('.buttons-pdf').trigger();
    });

    $('#printTable').on('click', function() {
        table.button('.buttons-print').trigger();
    });

    // Store permissions data for JavaScript use
    var permissionsData = @json($permissions ?? []);

    // Handle Add Role Form Submission
    $('#addRoleForm').on('submit', function(e) {
        e.preventDefault();

        $.ajax({
            url: $(this).attr('action'),
            type: 'POST',
            data: $(this).serialize(),
            success: function(response) {
                closeModal('addRoleModal');
                $('#addRoleForm')[0].reset();
                swal("تم الحفظ!", "تم إضافة الدور بنجاح.", "success");
                table.draw();
            },
            error: function(xhr) {
                var errors = xhr.responseJSON.errors;
                var errorMessage = 'حدث خطأ أثناء إضافة الدور:\n';
                if (errors) {
                    Object.keys(errors).forEach(function(key) {
                        errorMessage += errors[key][0] + '\n';
                    });
                }
                swal("خطأ!", errorMessage, "error");
            }
        });
    });

    // Handle Edit Role Form Submission
    $('#editRoleForm').on('submit', function(e) {
        e.preventDefault();
        
        $.ajax({
            url: $(this).attr('action'),
            type: 'PUT',
            data: $(this).serialize(),
            success: function(response) {
                closeModal('editRoleModal');
                swal("تم التحديث!", "تم تحديث بيانات الدور بنجاح.", "success");
                table.draw();
            },
            error: function(xhr) {
                var errors = xhr.responseJSON.errors;
                var errorMessage = 'حدث خطأ أثناء تحديث الدور:\n';
                if (errors) {
                    Object.keys(errors).forEach(function(key) {
                        errorMessage += errors[key][0] + '\n';
                    });
                }
                swal("خطأ!", errorMessage, "error");
            }
        });
    });

    // Edit role function
    window.editRole = function(id) {
        var showUrl = "{{ route('roles.show', ':id') }}".replace(':id', id);
        $.get(showUrl, function(data) {
            $('#edit_name').val(data.name);
            $('#edit_guard_name').val(data.guard_name);
            
            var updateUrl = "{{ route('roles.update', ':id') }}".replace(':id', id);
            $('#editRoleForm').attr('action', updateUrl);
            
            // Load permissions for editing
            loadPermissionsForEdit(data.permissions || []);
            
            openModal('editRoleModal');
        }).fail(function() {
            swal("خطأ!", "حدث خطأ أثناء تحميل بيانات الدور.", "error");
        });
    };

    // Load permissions for edit modal
    function loadPermissionsForEdit(rolePermissions) {
        var permissionsHtml = '<div class="grid grid-cols-1 md:grid-cols-2 gap-4">';

        Object.keys(permissionsData).forEach(function(group) {
            permissionsHtml += '<div class="bg-gray-50 rounded-lg p-3">';
            permissionsHtml += '<div class="flex items-center mb-3 pb-2 border-b border-gray-200">';
            permissionsHtml += '<input type="checkbox" class="group-checkbox h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded" data-group="' + group + '">';
            permissionsHtml += '<label class="mr-2 text-sm font-medium text-gray-900">' + group.charAt(0).toUpperCase() + group.slice(1) + '</label>';
            permissionsHtml += '</div>';
            permissionsHtml += '<div class="space-y-2">';

            permissionsData[group].forEach(function(permission) {
                permissionsHtml += '<div class="flex items-center">';
                permissionsHtml += '<input class="permission-checkbox h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded" type="checkbox"';
                permissionsHtml += ' name="permissions[]" value="' + permission.name + '" id="edit_perm_' + permission.id + '" data-group="' + group + '"';

                // Check if role has this permission
                var hasPermission = false;
                if (rolePermissions && Array.isArray(rolePermissions)) {
                    hasPermission = rolePermissions.some(function(p) {
                        return p.name === permission.name;
                    });
                }
                if (hasPermission) permissionsHtml += ' checked';

                permissionsHtml += '>';
                permissionsHtml += '<label class="mr-2 text-sm text-gray-700" for="edit_perm_' + permission.id + '">';
                permissionsHtml += permission.name;
                permissionsHtml += '</label>';
                permissionsHtml += '</div>';
            });

            permissionsHtml += '</div>';
            permissionsHtml += '</div>';
        });

        permissionsHtml += '</div>';
        $('.edit-permissions-container').html(permissionsHtml);

        // Re-bind group checkbox events
        bindGroupCheckboxEvents();
    }

    // Delete role
    window.deleteRole = function(id) {
        swal({
            title: "هل أنت متأكد؟",
            text: "لن تتمكن من التراجع عن هذا الإجراء!",
            type: "warning",
            showCancelButton: true,
            confirmButtonColor: "#DD6B55",
            confirmButtonText: "نعم، احذف!",
            cancelButtonText: "إلغاء",
            closeOnConfirm: false,
            closeOnCancel: false
        }, function(isConfirm) {
            if (isConfirm) {
                var deleteUrl = "{{ route('roles.destroy', ':id') }}".replace(':id', id);
                $.ajax({
                    url: deleteUrl,
                    type: 'DELETE',
                    data: {
                        _token: "{{ csrf_token() }}"
                    },
                    success: function(response) {
                        swal("تم الحذف!", "تم حذف الدور بنجاح.", "success");
                        table.draw();
                    },
                    error: function(xhr) {
                        swal("خطأ!", "حدث خطأ أثناء حذف الدور.", "error");
                    }
                });
            } else {
                swal("تم الإلغاء", "لم يتم حذف الدور.", "error");
            }
        });
    };

    // Group checkbox functionality
    function bindGroupCheckboxEvents() {
        $(document).on('change', '.group-checkbox', function() {
            var group = $(this).data('group');
            var isChecked = $(this).is(':checked');
            
            $('input[data-group="' + group + '"].permission-checkbox').prop('checked', isChecked);
        });

        $(document).on('change', '.permission-checkbox', function() {
            var group = $(this).data('group');
            var totalInGroup = $('input[data-group="' + group + '"].permission-checkbox').length;
            var checkedInGroup = $('input[data-group="' + group + '"].permission-checkbox:checked').length;
            
            var groupCheckbox = $('input[data-group="' + group + '"].group-checkbox');
            
            if (checkedInGroup === 0) {
                groupCheckbox.prop('checked', false).prop('indeterminate', false);
            } else if (checkedInGroup === totalInGroup) {
                groupCheckbox.prop('checked', true).prop('indeterminate', false);
            } else {
                groupCheckbox.prop('checked', false).prop('indeterminate', true);
            }
        });
    }

    // Initialize group checkbox events
    bindGroupCheckboxEvents();
});

// Modal functions
function openModal(modalId) {
    document.getElementById(modalId).classList.remove('hidden');
    document.body.style.overflow = 'hidden';
}

function closeModal(modalId) {
    document.getElementById(modalId).classList.add('hidden');
    document.body.style.overflow = 'auto';
}

// Close modal when clicking outside
document.addEventListener('click', function(event) {
    const modals = ['addRoleModal', 'editRoleModal'];
    modals.forEach(function(modalId) {
        const modal = document.getElementById(modalId);
        if (event.target === modal) {
            closeModal(modalId);
        }
    });
});
</script>
@endpush