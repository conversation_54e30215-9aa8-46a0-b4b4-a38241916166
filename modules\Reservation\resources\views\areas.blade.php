@extends('layouts.master')

@push('styles')
<!-- Bootstrap CSS -->
<link href="https://cdn.jsdelivr.net/npm/bootstrap@4.6.2/dist/css/bootstrap.min.css" rel="stylesheet">
<!-- DataTables CSS -->
<link rel="stylesheet" href="https://cdn.datatables.net/1.13.7/css/dataTables.bootstrap4.min.css">
<link rel="stylesheet" href="https://cdn.datatables.net/buttons/2.4.2/css/buttons.bootstrap4.min.css">
<link rel="stylesheet" href="https://cdn.datatables.net/responsive/2.5.0/css/responsive.bootstrap4.min.css">
<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css">
<!-- SweetAlert CSS -->
{{-- <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/sweetalert@2.1.2/dist/sweetalert.min.css"> --}}
<style>
/* Status badge styles */
.status-available { 
    @apply bg-green-100 text-green-800 border border-green-200; 
}
.status-occupied { 
    @apply bg-red-100 text-red-800 border border-red-200; 
}
.status-reserved { 
    @apply bg-yellow-100 text-yellow-800 border border-yellow-200; 
}
.status-cleaning { 
    @apply bg-blue-100 text-blue-800 border border-blue-200; 
}
.status-out_of_order { 
    @apply bg-gray-100 text-gray-800 border border-gray-200; 
}

/* Custom animations */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.animate-fade-in-up {
    animation: fadeInUp 0.3s ease-out;
}

/* Area card styles */
.area-card {
    @apply transition-all duration-300 ease-in-out border-0 rounded-2xl shadow-lg overflow-hidden relative;
}
.area-card:hover {
    @apply -translate-y-1 shadow-2xl;
}
.area-card.bg-primary-gradient {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    @apply text-white;
}
.area-card.bg-success-gradient {
    background: linear-gradient(135deg, #1cc88a 0%, #13855c 100%);
    @apply text-white;
}
.area-card.bg-warning-gradient {
    background: linear-gradient(135deg, #f6c23e 0%, #dda20a 100%);
    @apply text-white;
}
.area-card.bg-info-gradient {
    background: linear-gradient(135deg, #36b9cc 0%, #258391 100%);
    @apply text-white;
}
.area-card.bg-danger-gradient {
    background: linear-gradient(135deg, #e74a3b 0%, #c0392b 100%);
    @apply text-white;
}
.area-card.bg-secondary-gradient {
    background: linear-gradient(135deg, #858796 0%, #60616f 100%);
    @apply text-white;
}
.area-stats {
    @apply absolute top-4 right-4 bg-white bg-opacity-20 px-3 py-1 rounded-full text-xs font-semibold;
}
.area-icon {
    @apply text-4xl mb-4 opacity-80;
}
.card-stats {
    @apply flex justify-between mt-4;
}
.stat-item {
    @apply text-center flex-1;
}
.stat-number {
    @apply text-2xl font-bold mb-1;
}
.stat-label {
    @apply text-xs opacity-90;
}
.view-toggle-btn {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    @apply border-0 text-white rounded-full px-5 py-2 transition-all duration-300 ease-in-out;
}
.view-toggle-btn:hover {
    @apply -translate-y-0.5 text-white;
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
}
.add-area-btn {
    background: linear-gradient(135deg, #1cc88a 0%, #13855c 100%);
    @apply border-0 text-white rounded-full px-5 py-2 transition-all duration-300 ease-in-out;
}
.add-area-btn:hover {
    @apply -translate-y-0.5 text-white;
    box-shadow: 0 4px 15px rgba(28, 200, 138, 0.4);
}
.table-view {
    @apply bg-white rounded-2xl shadow-lg p-5;
}

/* Responsive grid improvements */
@media (max-width: 640px) {
    #areasContainer {
        grid-template-columns: 1fr;
    }
}

@media (min-width: 641px) and (max-width: 1024px) {
    #areasContainer {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media (min-width: 1025px) {
    #areasContainer {
        grid-template-columns: repeat(3, 1fr);
    }
}

@media (min-width: 1280px) {
    #areasContainer {
        grid-template-columns: repeat(4, 1fr);
    }
}
</style>
@endpush

@section('title')
إدارة المناطق
@stop

@section('page-header')
<!-- breadcrumb -->
<div class="flex justify-between items-center">
    <div class="my-auto">
        <div class="flex items-center">
            <h4 class="text-lg font-semibold text-gray-800 mb-0 my-auto">إدارة المناطق</h4>
            <span class="text-gray-500 text-sm mr-2 mb-0">/ المناطق</span>
        </div>
    </div>
    <div class="flex my-xl-auto">
        <div class="pr-1 mb-3 mb-xl-0">
            <button type="button" class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-md transition-colors duration-200 ml-2 flex items-center gap-2" data-toggle="modal" data-target="#addAreaModal">
                <i class="mdi mdi-plus"></i>
                <span>إضافة منطقة</span>
            </button>
        </div>
    </div>
</div>
<!-- breadcrumb -->
@endsection

@section('content')
<!-- row -->
<div class="grid grid-cols-1 gap-6">
    <div class="col-span-full">
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 mb-6">
            <div class="px-6 py-4 border-b border-gray-200">
                <div class="flex justify-between items-center">
                    <h4 class="text-lg font-semibold text-gray-800 mb-0">قائمة المناطق</h4>
                    <div class="view-toggle">
                        <div class="inline-flex rounded-md shadow-sm" role="group">
                            <button type="button" class="px-4 py-2 text-sm font-medium text-blue-600 bg-blue-50 border border-blue-200 rounded-l-md hover:bg-blue-100 focus:z-10 focus:ring-2 focus:ring-blue-500 focus:bg-blue-100 active" id="cards-view-btn">
                                <i class="mdi mdi-view-grid"></i> عرض البطاقات
                            </button>
                            <button type="button" class="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-200 rounded-r-md hover:bg-gray-50 focus:z-10 focus:ring-2 focus:ring-blue-500 focus:bg-gray-50" id="table-view-btn">
                                <i class="mdi mdi-table"></i> عرض جدولي
                            </button>
                        </div>
                    </div>
                </div>
                <p class="text-sm text-gray-500 mb-2">إدارة جميع مناطق المطعم</p>
            </div>
            <div class="p-6">
                <!-- Cards View -->
                <div id="cards-view">
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4" id="areasContainer">
                        <!-- Areas will be loaded here -->
                    </div>
                </div>

                <!-- Table View -->
                <div id="table-view" style="display: none;">
                    <div class="overflow-x-auto">
                        <table id="areas-table" class="min-w-full divide-y divide-gray-200">
                            <thead class="bg-gray-50">
                                <tr>
                                    <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">#</th>
                                    <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">اسم المنطقة</th>
                                    <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">الوصف</th>
                                    <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">عدد الطاولات</th>
                                    <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">السعة الإجمالية</th>
                                    <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">الطاولات المتاحة</th>
                                    <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody class="bg-white divide-y divide-gray-200">
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<!-- row closed -->

<!-- Add/Edit Area Modal -->
<div class="modal fade" id="addAreaModal" tabindex="-1" role="dialog" aria-labelledby="addAreaModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content rounded-lg shadow-xl">
            <div class="modal-header bg-gradient-to-r from-blue-500 to-purple-600 text-white rounded-t-lg">
                <h5 class="modal-title text-lg font-semibold" id="addAreaModalLabel">إضافة منطقة جديدة</h5>
                <button type="button" class="close text-white hover:text-gray-200" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <form id="areaForm">
                <div class="modal-body p-6">
                    <input type="hidden" id="area_id" name="area_id">
                    <div class="grid grid-cols-1 gap-4">
                        <div class="col-span-1">
                            <div class="form-group">
                                <label for="name" class="block text-sm font-medium text-gray-700 mb-2">اسم المنطقة <span class="text-red-500">*</span></label>
                                <input type="text" class="form-control w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent" id="name" name="name" required>
                            </div>
                        </div>
                    </div>
                    <div class="grid grid-cols-1 gap-4 mt-4">
                        <div class="col-span-1">
                            <div class="form-group">
                                <label for="description" class="block text-sm font-medium text-gray-700 mb-2">الوصف</label>
                                <textarea class="form-control w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent" id="description" name="description" rows="3"></textarea>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer bg-gray-50 px-6 py-3 rounded-b-lg flex justify-end space-x-3">
                    <button type="button" class="btn bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded-md transition-colors duration-200" data-dismiss="modal">إلغاء</button>
                    <button type="submit" class="btn bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-md transition-colors duration-200">حفظ</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Show Area Modal -->
<div class="modal fade" id="showAreaModal" tabindex="-1" role="dialog" aria-labelledby="showAreaModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content rounded-lg shadow-xl">
            <div class="modal-header bg-gradient-to-r from-green-500 to-blue-600 text-white rounded-t-lg">
                <h5 class="modal-title text-lg font-semibold" id="showAreaModalLabel">تفاصيل المنطقة</h5>
                <button type="button" class="close text-white hover:text-gray-200" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body p-6">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div class="col-span-1">
                        <div class="form-group">
                            <label class="block text-sm font-semibold text-gray-700 mb-2">اسم المنطقة:</label>
                            <p id="show_name" class="text-gray-900 bg-gray-50 p-3 rounded-md"></p>
                        </div>
                    </div>
                    <div class="col-span-1">
                        <div class="form-group">
                            <label class="block text-sm font-semibold text-gray-700 mb-2">عدد الطاولات:</label>
                            <p id="show_tables_count" class="text-gray-900 bg-gray-50 p-3 rounded-md"></p>
                        </div>
                    </div>
                </div>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mt-4">
                    <div class="col-span-1">
                        <div class="form-group">
                            <label class="block text-sm font-semibold text-gray-700 mb-2">السعة الإجمالية:</label>
                            <p id="show_total_capacity" class="text-gray-900 bg-gray-50 p-3 rounded-md"></p>
                        </div>
                    </div>
                    <div class="col-span-1">
                        <div class="form-group">
                            <label class="block text-sm font-semibold text-gray-700 mb-2">الطاولات المتاحة:</label>
                            <p id="show_available_tables" class="text-gray-900 bg-gray-50 p-3 rounded-md"></p>
                        </div>
                    </div>
                </div>
                <div class="grid grid-cols-1 gap-4 mt-4">
                    <div class="col-span-1">
                        <div class="form-group">
                            <label class="block text-sm font-semibold text-gray-700 mb-2">الوصف:</label>
                            <p id="show_description" class="text-gray-900 bg-gray-50 p-3 rounded-md min-h-[80px]"></p>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer bg-gray-50 px-6 py-3 rounded-b-lg flex justify-end">
                <button type="button" class="btn bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded-md transition-colors duration-200" data-dismiss="modal">إغلاق</button>
            </div>
        </div>
    </div>
</div>

<!-- Area Tables Modal -->
<div class="modal fade" id="areaTablesModal" tabindex="-1" role="dialog" aria-labelledby="areaTablesModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-xl" role="document">
        <div class="modal-content rounded-lg shadow-xl">
            <div class="modal-header bg-gradient-to-r from-indigo-500 to-purple-600 text-white rounded-t-lg">
                <h5 class="modal-title text-lg font-semibold" id="areaTablesModalLabel">طاولات المنطقة</h5>
                <button type="button" class="close text-white hover:text-gray-200" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body p-6">
                <div id="areaTablesContent">
                    <!-- Tables will be loaded here -->
                </div>
            </div>
            <div class="modal-footer bg-gray-50 px-6 py-3 rounded-b-lg flex justify-end">
                <button type="button" class="btn bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded-md transition-colors duration-200" data-dismiss="modal">إغلاق</button>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<!-- Bootstrap JS -->
<script src="https://cdn.jsdelivr.net/npm/bootstrap@4.6.2/dist/js/bootstrap.bundle.min.js"></script>
<!-- DataTables JS -->
<script src="https://cdn.datatables.net/1.13.7/js/jquery.dataTables.min.js"></script>

<script src="https://cdn.datatables.net/responsive/2.5.0/js/dataTables.responsive.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>
<!-- SweetAlert JS -->
<script src="https://cdn.jsdelivr.net/npm/sweetalert@2.1.2/dist/sweetalert.min.js"></script>

<script>
$(document).ready(function() {
    let currentView = 'card';
    let areasData = [];

    // Load areas
    loadAreas();

    // View toggle functionality
    $('#cards-view-btn').click(function() {
        $('#cards-view').show();
        $('#table-view').hide();
        $(this).addClass('text-blue-600 bg-blue-50').removeClass('text-gray-700 bg-white');
        $('#table-view-btn').addClass('text-gray-700 bg-white').removeClass('text-blue-600 bg-blue-50');
        currentView = 'card';
    });

    $('#table-view-btn').click(function() {
        $('#cards-view').hide();
        $('#table-view').show();
        $(this).addClass('text-blue-600 bg-blue-50').removeClass('text-gray-700 bg-white');
        $('#cards-view-btn').addClass('text-gray-700 bg-white').removeClass('text-blue-600 bg-blue-50');
        currentView = 'table';
        initDataTable();
    });

    function loadAreas() {
        $.ajax({
            url: '{{ route("areas.data") }}',
            type: 'GET',
            headers: {
                'X-Requested-With': 'XMLHttpRequest',
                'Accept': 'application/json'
            },
            success: function(response) {
                console.log('Areas response:', response);
                areasData = response.data || response;
                
                // Ensure areasData is an array
                if (!Array.isArray(areasData)) {
                    console.warn('Response is not an array, attempting to extract data:', areasData);
                    if (areasData && typeof areasData === 'object' && areasData.data) {
                        areasData = areasData.data;
                    } else {
                        areasData = [];
                    }
                }
                
                renderCards();
            },
            error: function(xhr) {
                console.error('Error loading areas:', xhr);
                swal('خطأ', 'حدث خطأ أثناء تحميل المناطق', 'error');
                areasData = [];
                renderCards();
            }
        });
    }

    function renderCards() {
        let html = '';
        const gradients = ['bg-primary-gradient', 'bg-success-gradient', 'bg-warning-gradient', 'bg-info-gradient', 'bg-danger-gradient', 'bg-secondary-gradient'];
        
        // Ensure areasData is an array
        if (!Array.isArray(areasData)) {
            console.error('areasData is not an array:', areasData);
            $('#areasContainer').html('<div class="col-span-full"><p class="text-center text-gray-500 py-8">لا توجد مناطق متاحة</p></div>');
            return;
        }
        
        if (areasData.length === 0) {
            $('#areasContainer').html('<div class="col-span-full"><p class="text-center text-gray-500 py-8">لا توجد مناطق متاحة</p></div>');
            return;
        }
        
        areasData.forEach(function(area, index) {
            let gradientClass = gradients[index % gradients.length];
            
            html += `
                <div class="col-span-1 animate-fade-in-up">
                    <div class="card area-card ${gradientClass} h-full">
                        <div class="area-stats">
                            ${area.tables_count || 0} طاولة
                        </div>
                        <div class="card-body text-center p-6">
                            <div class="area-icon">
                                <i class="mdi mdi-map-marker-outline"></i>
                            </div>
                            <h5 class="card-title mb-3 text-lg font-semibold">${area.name || 'غير محدد'}</h5>
                            <p class="card-text mb-3 text-sm opacity-90">${area.description || 'لا يوجد وصف'}</p>
                            
                            <div class="card-stats">
                                <div class="stat-item">
                                    <div class="stat-number">${area.total_capacity || 0}</div>
                                    <div class="stat-label">السعة الإجمالية</div>
                                </div>
                                <div class="stat-item">
                                    <div class="stat-number">${area.available_tables || 0}</div>
                                    <div class="stat-label">طاولات متاحة</div>
                                </div>
                                <div class="stat-item">
                                    <div class="stat-number">${area.occupied_tables || 0}</div>
                                    <div class="stat-label">طاولات مشغولة</div>
                                </div>
                            </div>
                        </div>
                        <div class="card-footer bg-transparent border-0 p-4">
                            <div class="btn-group w-full flex" role="group">
                                <button type="button" class="btn bg-white bg-opacity-20 hover:bg-opacity-30 text-white border-0 flex-1 py-2 transition-all duration-200" onclick="showArea(${area.id})" title="عرض">
                                    <i class="mdi mdi-eye text-blue-200"></i>
                                </button>
                                <button type="button" class="btn bg-white bg-opacity-20 hover:bg-opacity-30 text-white border-0 flex-1 py-2 transition-all duration-200" onclick="showAreaTables(${area.id})" title="عرض الطاولات">
                                    <i class="mdi mdi-table-furniture text-cyan-200"></i>
                                </button>
                                <button type="button" class="btn bg-white bg-opacity-20 hover:bg-opacity-30 text-white border-0 flex-1 py-2 transition-all duration-200" onclick="editArea(${area.id})" title="تعديل">
                                    <i class="mdi mdi-pencil text-yellow-200"></i>
                                </button>
                                <button type="button" class="btn bg-white bg-opacity-20 hover:bg-opacity-30 text-white border-0 flex-1 py-2 transition-all duration-200" onclick="deleteArea(${area.id})" title="حذف">
                                    <i class="mdi mdi-delete text-red-200"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            `;
        });
        $('#areasContainer').html(html);
    }

    function initDataTable() {
        if ($.fn.DataTable.isDataTable('#areas-table')) {
            return;
        }

        $('#areas-table').DataTable({
            processing: true,
            serverSide: true,
            ajax: {
                url: '{{ route("areas.data") }}',
                headers: {
                    'X-Requested-With': 'XMLHttpRequest',
                    'Accept': 'application/json'
                }
            },
            columns: [
                { data: 'DT_RowIndex', name: 'DT_RowIndex', orderable: false, searchable: false },
                { data: 'name', name: 'name' },
                { data: 'description', name: 'description' },
                { data: 'tables_count', name: 'tables_count' },
                { data: 'total_capacity', name: 'total_capacity' },
                { data: 'available_tables', name: 'available_tables' },
                { 
                    data: 'action', 
                    name: 'action', 
                    orderable: false, 
                    searchable: false,
                    render: function(data, type, row) {
                        return `
                            <div class="btn-group" role="group">
                                <button type="button" class="btn btn-sm btn-info" onclick="showArea(${row.id})" title="عرض">
                                    <i class="mdi mdi-eye"></i>
                                </button>
                                <button type="button" class="btn btn-sm btn-primary" onclick="showAreaTables(${row.id})" title="عرض الطاولات">
                                    <i class="mdi mdi-table-furniture"></i>
                                </button>
                                <button type="button" class="btn btn-sm btn-warning" onclick="editArea(${row.id})" title="تعديل">
                                    <i class="mdi mdi-pencil"></i>
                                </button>
                                <button type="button" class="btn btn-sm btn-danger" onclick="deleteArea(${row.id})" title="حذف">
                                    <i class="mdi mdi-delete"></i>
                                </button>
                            </div>
                        `;
                    }
                }
            ],
            language: {
                url: '//cdn.datatables.net/plug-ins/1.10.25/i18n/Arabic.json'
            }
        });
    }

    // Form submission
    $('#areaForm').submit(function(e) {
        e.preventDefault();
        let formData = $(this).serialize();
        let url = $('#area_id').val() ? '{{ url("/areas") }}/' + $('#area_id').val() : '{{ route("areas.store") }}';
        let method = $('#area_id').val() ? 'PUT' : 'POST';

        $.ajax({
            url: url,
            type: method,
            data: formData,
            headers: {
                'X-Requested-With': 'XMLHttpRequest',
                'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
            },
            success: function(response) {
                $('#addAreaModal').modal('hide');
                $('#areaForm')[0].reset();
                $('#area_id').val('');
                $('#addAreaModalLabel').text('إضافة منطقة جديدة');
                
                if (currentView === 'card') {
                    loadAreas();
                } else {
                    $('#areas-table').DataTable().ajax.reload();
                }
                
                swal('نجح', 'تم حفظ المنطقة بنجاح', 'success');
            },
            error: function(xhr) {
                console.error('Error saving area:', xhr);
                let errorMessage = 'حدث خطأ أثناء حفظ المنطقة';
                if (xhr.responseJSON && xhr.responseJSON.message) {
                    errorMessage = xhr.responseJSON.message;
                }
                swal('خطأ', errorMessage, 'error');
            }
        });
    });
});

function showArea(id) {
    $.ajax({
        url: '{{ url("/areas") }}/' + id,
        type: 'GET',
        headers: {
            'X-Requested-With': 'XMLHttpRequest',
            'Accept': 'application/json'
        },
        success: function(response) {
            let area = response.data || response;
            $('#show_name').text(area.name);
            $('#show_description').text(area.description || 'لا يوجد وصف');
            $('#show_tables_count').text((area.tables_count || 0) + ' طاولة');
            $('#show_total_capacity').text((area.total_capacity || 0) + ' شخص');
            $('#show_available_tables').text((area.available_tables || 0) + ' طاولة');
            
            $('#showAreaModal').modal('show');
        },
        error: function(xhr) {
            console.error('Error loading area:', xhr);
            swal('خطأ', 'حدث خطأ أثناء تحميل بيانات المنطقة', 'error');
        }
    });
}

function editArea(id) {
    $.ajax({
        url: '{{ url("/areas") }}/' + id,
        type: 'GET',
        headers: {
            'X-Requested-With': 'XMLHttpRequest',
            'Accept': 'application/json'
        },
        success: function(response) {
            let area = response.data || response;
            $('#area_id').val(area.id);
            $('#name').val(area.name);
            $('#description').val(area.description);
            $('#addAreaModalLabel').text('تعديل المنطقة');
            $('#addAreaModal').modal('show');
        },
        error: function(xhr) {
            console.error('Error loading area for edit:', xhr);
            swal('خطأ', 'حدث خطأ أثناء تحميل بيانات المنطقة للتعديل', 'error');
        }
    });
}

function showAreaTables(id) {
    $.ajax({
        url: '{{ url("/areas") }}/' + id + '/tables',
        type: 'GET',
        headers: {
            'X-Requested-With': 'XMLHttpRequest',
            'Accept': 'application/json'
        },
        success: function(response) {
            let tables = response.data || response;
            let html = '<div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">';
            
            if (tables.length > 0) {
                tables.forEach(function(table) {
                    let statusClass = 'bg-green-100 text-green-800';
                    let statusText = 'متاحة';
                    
                    if (table.status === 'occupied') {
                        statusClass = 'bg-red-100 text-red-800';
                        statusText = 'مشغولة';
                    } else if (table.status === 'reserved') {
                        statusClass = 'bg-yellow-100 text-yellow-800';
                        statusText = 'محجوزة';
                    }
                    
                    html += `
                        <div class="col-span-1">
                            <div class="bg-white rounded-lg shadow-md border border-gray-200 hover:shadow-lg transition-shadow duration-200">
                                <div class="p-4 text-center">
                                    <h6 class="text-lg font-semibold text-gray-800 mb-2">طاولة ${table.table_number}</h6>
                                    <p class="text-gray-600 mb-3">السعة: ${table.seating_capacity} شخص</p>
                                    <span class="inline-block px-3 py-1 rounded-full text-xs font-medium ${statusClass}">${statusText}</span>
                                </div>
                            </div>
                        </div>
                    `;
                });
            } else {
                html += '<div class="col-span-full"><p class="text-center text-gray-500 py-8">لا توجد طاولات في هذه المنطقة</p></div>';
            }
            
            html += '</div>';
            $('#areaTablesContent').html(html);
            $('#areaTablesModal').modal('show');
        },
        error: function(xhr) {
            console.error('Error loading area tables:', xhr);
            swal('خطأ', 'حدث خطأ أثناء تحميل طاولات المنطقة', 'error');
        }
    });
}

function deleteArea(id) {
    swal({
        title: 'هل أنت متأكد؟',
        text: 'سيتم حذف هذه المنطقة نهائياً!',
        icon: 'warning',
        buttons: {
            cancel: {
                text: 'إلغاء',
                value: null,
                visible: true,
                className: 'btn btn-secondary',
                closeModal: true,
            },
            confirm: {
                text: 'حذف',
                value: true,
                visible: true,
                className: 'btn btn-danger',
                closeModal: true
            }
        },
        dangerMode: true,
    }).then((willDelete) => {
        if (willDelete) {
            $.ajax({
                url: '{{ url("/areas") }}/' + id,
                type: 'DELETE',
                headers: {
                    'X-Requested-With': 'XMLHttpRequest',
                    'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                },
                success: function(response) {
                    if (currentView === 'card') {
                        loadAreas();
                    } else {
                        $('#areas-table').DataTable().ajax.reload();
                    }
                    swal('تم الحذف!', 'تم حذف المنطقة بنجاح', 'success');
                },
                error: function(xhr) {
                    console.error('Error deleting area:', xhr);
                    let errorMessage = 'حدث خطأ أثناء حذف المنطقة';
                    if (xhr.responseJSON && xhr.responseJSON.message) {
                        errorMessage = xhr.responseJSON.message;
                    }
                    swal('خطأ', errorMessage, 'error');
                }
            });
        }
    });
}
</script>
@endpush