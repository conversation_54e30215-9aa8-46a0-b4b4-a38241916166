@extends('layouts.app')

@section('title', 'Payment - Order #' . $order->order_number)

@push('styles')
<style>
    .payment-container {
        max-width: 1200px;
        margin: 0 auto;
        padding: 20px;
    }
    .payment-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 20px;
        border-radius: 10px;
        margin-bottom: 30px;
    }
    .order-summary {
        background: white;
        border-radius: 10px;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        padding: 20px;
        margin-bottom: 30px;
    }
    .payment-methods {
        background: white;
        border-radius: 10px;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        padding: 20px;
    }
    .payment-method-card {
        border: 2px solid #e5e7eb;
        border-radius: 8px;
        padding: 15px;
        margin-bottom: 15px;
        cursor: pointer;
        transition: all 0.3s ease;
    }
    .payment-method-card:hover {
        border-color: #3b82f6;
        background-color: #f8fafc;
    }
    .payment-method-card.selected {
        border-color: #3b82f6;
        background-color: #eff6ff;
    }
    .payment-method-icon {
        width: 40px;
        height: 40px;
        border-radius: 8px;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 15px;
    }
    .amount-input {
        font-size: 24px;
        font-weight: bold;
        text-align: center;
        border: 2px solid #e5e7eb;
        border-radius: 8px;
        padding: 15px;
        margin-bottom: 20px;
    }
    .amount-input:focus {
        border-color: #3b82f6;
        outline: none;
    }
    .quick-amounts {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));
        gap: 10px;
        margin-bottom: 20px;
    }
    .quick-amount-btn {
        padding: 10px;
        border: 2px solid #e5e7eb;
        border-radius: 6px;
        background: white;
        cursor: pointer;
        transition: all 0.3s ease;
        font-weight: 500;
    }
    .quick-amount-btn:hover {
        border-color: #3b82f6;
        background-color: #eff6ff;
    }
    .payment-summary {
        background: #f8fafc;
        border-radius: 8px;
        padding: 20px;
        margin-bottom: 20px;
    }
    .summary-line {
        display: flex;
        justify-content: space-between;
        margin-bottom: 10px;
    }
    .summary-line.total {
        font-size: 18px;
        font-weight: bold;
        border-top: 2px solid #e5e7eb;
        padding-top: 10px;
        margin-top: 10px;
    }
    .process-payment-btn {
        width: 100%;
        background: linear-gradient(135deg, #10b981 0%, #059669 100%);
        color: white;
        border: none;
        border-radius: 8px;
        padding: 15px;
        font-size: 18px;
        font-weight: bold;
        cursor: pointer;
        transition: all 0.3s ease;
    }
    .process-payment-btn:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 15px rgba(16, 185, 129, 0.3);
    }
    .process-payment-btn:disabled {
        background: #9ca3af;
        cursor: not-allowed;
        transform: none;
        box-shadow: none;
    }
</style>
@endpush

@section('content')
<div class="payment-container">
    <!-- Header -->
    <div class="payment-header">
        <div class="flex justify-between items-center">
            <div>
                <h1 class="text-2xl font-bold mb-2">Payment Processing</h1>
                <p class="text-blue-100">Order #{{ $order->order_number }} - {{ $order->created_at->format('d/m/Y H:i') }}</p>
            </div>
            <div class="text-right">
                @if($order->table)
                    <div class="text-blue-100">Table: {{ $order->table->name }}</div>
                @endif
                @if($order->customer)
                    <div class="text-blue-100">Customer: {{ $order->customer->name }}</div>
                @endif
            </div>
        </div>
    </div>

    <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
        <!-- Order Summary -->
        <div class="order-summary">
            <h2 class="text-xl font-bold mb-4">Order Summary</h2>
            
            <div class="space-y-3 mb-6">
                @foreach($order->orderItems as $item)
                    <div class="flex justify-between items-start">
                        <div class="flex-1">
                            <div class="font-medium">{{ $item->quantity }}x {{ $item->menuItem->name }}</div>
                            @if($item->variant_name)
                                <div class="text-sm text-gray-600">Variant: {{ $item->variant_name }}</div>
                            @endif
                            @if($item->notes)
                                <div class="text-sm text-gray-600">Notes: {{ $item->notes }}</div>
                            @endif
                            @if($item->addons && count($item->addons) > 0)
                                @foreach($item->addons as $addon)
                                    <div class="text-sm text-gray-600">+ {{ $addon['addon_name'] }} ({{ $addon['quantity'] }}x)</div>
                                @endforeach
                            @endif
                        </div>
                        <div class="font-bold">${{ number_format($item->total_price, 2) }}</div>
                    </div>
                @endforeach
            </div>

            <div class="border-t pt-4">
                <div class="summary-line">
                    <span>Subtotal:</span>
                    <span>${{ number_format($order->subtotal, 2) }}</span>
                </div>
                @if($order->discount_amount > 0)
                    <div class="summary-line">
                        <span>Discount:</span>
                        <span>-${{ number_format($order->discount_amount, 2) }}</span>
                    </div>
                @endif
                @if($order->tax_amount > 0)
                    <div class="summary-line">
                        <span>Tax:</span>
                        <span>${{ number_format($order->tax_amount, 2) }}</span>
                    </div>
                @endif
                <div class="summary-line total">
                    <span>Total Amount:</span>
                    <span>${{ number_format($order->total_amount, 2) }}</span>
                </div>
                @if($order->transaction && $order->transaction->paid_amount > 0)
                    <div class="summary-line">
                        <span>Paid Amount:</span>
                        <span>${{ number_format($order->transaction->paid_amount, 2) }}</span>
                    </div>
                    <div class="summary-line total text-red-600">
                        <span>Amount Due:</span>
                        <span>${{ number_format($order->transaction->due_amount, 2) }}</span>
                    </div>
                @endif
            </div>
        </div>

        <!-- Payment Methods -->
        <div class="payment-methods">
            <h2 class="text-xl font-bold mb-4">Payment Method</h2>
            
            <form id="paymentForm">
                @csrf
                <input type="hidden" name="transaction_id" value="{{ $order->transaction->id }}">
                
                <!-- Payment Methods -->
                <div class="mb-6">
                    @foreach($paymentMethods as $method)
                        <div class="payment-method-card" data-method-id="{{ $method->id }}">
                            <div class="flex items-center">
                                <div class="payment-method-icon bg-{{ $method->color ?? 'blue' }}-100">
                                    <i class="mdi mdi-{{ $method->icon ?? 'credit-card' }} text-{{ $method->color ?? 'blue' }}-600 text-xl"></i>
                                </div>
                                <div class="flex-1">
                                    <div class="font-medium">{{ $method->name }}</div>
                                    @if($method->description)
                                        <div class="text-sm text-gray-600">{{ $method->description }}</div>
                                    @endif
                                </div>
                                <div class="text-right">
                                    <input type="radio" name="payment_method_id" value="{{ $method->id }}" class="w-5 h-5">
                                </div>
                            </div>
                        </div>
                    @endforeach
                </div>

                <!-- Payment Amount -->
                <div class="mb-6">
                    <label class="block text-sm font-medium text-gray-700 mb-2">Payment Amount</label>
                    <input type="number" 
                           id="paymentAmount" 
                           name="amount" 
                           class="amount-input w-full" 
                           placeholder="0.00" 
                           step="0.01" 
                           min="0.01"
                           value="{{ $order->transaction->due_amount }}"
                           required>
                    
                    <!-- Quick Amount Buttons -->
                    <div class="quick-amounts">
                        <button type="button" class="quick-amount-btn" data-amount="{{ $order->transaction->due_amount }}">Exact</button>
                        <button type="button" class="quick-amount-btn" data-amount="10">$10</button>
                        <button type="button" class="quick-amount-btn" data-amount="20">$20</button>
                        <button type="button" class="quick-amount-btn" data-amount="50">$50</button>
                        <button type="button" class="quick-amount-btn" data-amount="100">$100</button>
                    </div>
                </div>

                <!-- Payment Summary -->
                <div class="payment-summary">
                    <div class="summary-line">
                        <span>Payment Amount:</span>
                        <span id="displayPaymentAmount">${{ number_format($order->transaction->due_amount, 2) }}</span>
                    </div>
                    <div class="summary-line">
                        <span>Amount Due:</span>
                        <span>${{ number_format($order->transaction->due_amount, 2) }}</span>
                    </div>
                    <div class="summary-line total" id="changeAmount" style="display: none;">
                        <span>Change:</span>
                        <span id="displayChange">$0.00</span>
                    </div>
                </div>

                <!-- Process Payment Button -->
                <button type="submit" class="process-payment-btn" id="processPaymentBtn" disabled>
                    <i class="mdi mdi-credit-card mr-2"></i>
                    Process Payment
                </button>
            </form>

            <!-- Back to POS Button -->
            <div class="mt-4">
                <a href="{{ route('pos.create') }}" class="block w-full text-center bg-gray-500 hover:bg-gray-600 text-white py-3 px-4 rounded-lg transition-colors">
                    <i class="mdi mdi-arrow-left mr-2"></i>
                    Back to POS
                </a>
            </div>
        </div>
    </div>
</div>

<!-- Loading Overlay -->
<div id="loadingOverlay" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50" style="display: none;">
    <div class="bg-white p-6 rounded-lg">
        <div class="flex items-center">
            <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mr-3"></div>
            <span>Processing payment...</span>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
<script>
$(document).ready(function() {
    const dueAmount = {{ $order->transaction->due_amount }};

    // Payment method selection
    $('.payment-method-card').on('click', function() {
        $('.payment-method-card').removeClass('selected');
        $(this).addClass('selected');
        $(this).find('input[type="radio"]').prop('checked', true);
        updateProcessButton();
    });

    // Quick amount buttons
    $('.quick-amount-btn').on('click', function() {
        const amount = $(this).data('amount');
        $('#paymentAmount').val(amount);
        updatePaymentSummary();
    });

    // Payment amount input
    $('#paymentAmount').on('input', function() {
        updatePaymentSummary();
    });

    // Update payment summary
    function updatePaymentSummary() {
        const paymentAmount = parseFloat($('#paymentAmount').val()) || 0;
        $('#displayPaymentAmount').text('$' + paymentAmount.toFixed(2));

        const change = paymentAmount - dueAmount;
        if (change > 0) {
            $('#changeAmount').show();
            $('#displayChange').text('$' + change.toFixed(2));
        } else {
            $('#changeAmount').hide();
        }

        updateProcessButton();
    }

    // Update process button state
    function updateProcessButton() {
        const hasPaymentMethod = $('input[name="payment_method_id"]:checked').length > 0;
        const hasAmount = parseFloat($('#paymentAmount').val()) > 0;

        $('#processPaymentBtn').prop('disabled', !(hasPaymentMethod && hasAmount));
    }

    // Process payment form
    $('#paymentForm').on('submit', function(e) {
        e.preventDefault();

        const paymentAmount = parseFloat($('#paymentAmount').val());
        if (paymentAmount <= 0) {
            Swal.fire('Error', 'Please enter a valid payment amount', 'error');
            return;
        }

        const paymentMethodId = $('input[name="payment_method_id"]:checked').val();
        if (!paymentMethodId) {
            Swal.fire('Error', 'Please select a payment method', 'error');
            return;
        }

        // Show loading
        $('#loadingOverlay').show();

        // Process payment
        $.ajax({
            url: '{{ route("api.payments.store") }}',
            method: 'POST',
            headers: {
                'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content'),
                'Content-Type': 'application/json',
                'Accept': 'application/json'
            },
            data: JSON.stringify({
                transaction_id: {{ $order->transaction->id }},
                payment_method_id: paymentMethodId,
                amount: paymentAmount,
                payment_date: new Date().toISOString().split('T')[0],
                notes: 'POS Payment'
            }),
            success: function(response) {
                $('#loadingOverlay').hide();

                if (response.success) {
                    const change = paymentAmount - dueAmount;
                    let message = 'Payment processed successfully!';
                    if (change > 0) {
                        message += `\n\nChange to give: $${change.toFixed(2)}`;
                    }

                    Swal.fire({
                        icon: 'success',
                        title: 'Payment Successful!',
                        text: message,
                        confirmButtonText: 'Print Receipt'
                    }).then((result) => {
                        if (result.isConfirmed) {
                            // Print receipt
                            window.open(`{{ route('pos.orders.bill', $order) }}`, '_blank');
                        }
                        // Redirect back to POS
                        window.location.href = '{{ route("pos.create") }}';
                    });
                } else {
                    Swal.fire('Error', response.message || 'Payment failed', 'error');
                }
            },
            error: function(xhr) {
                $('#loadingOverlay').hide();

                let errorMessage = 'Payment processing failed';
                if (xhr.responseJSON && xhr.responseJSON.message) {
                    errorMessage = xhr.responseJSON.message;
                }

                Swal.fire('Error', errorMessage, 'error');
            }
        });
    });

    // Initialize
    updatePaymentSummary();
});
</script>
@endpush
