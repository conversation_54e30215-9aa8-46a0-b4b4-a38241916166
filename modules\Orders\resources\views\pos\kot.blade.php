@extends('layouts.app')

@section('title', 'KOT - Order #' . $order->order_number)

@push('styles')
<style>
    .kot-container {
        max-width: 800px;
        margin: 0 auto;
        padding: 20px;
    }
    .kot-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 20px;
        border-radius: 10px;
        margin-bottom: 30px;
        text-align: center;
    }
    .kot-content {
        background: white;
        border-radius: 10px;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        padding: 30px;
    }
    .order-info {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 20px;
        margin-bottom: 30px;
        padding-bottom: 20px;
        border-bottom: 2px solid #e5e7eb;
    }
    .info-item {
        text-align: center;
    }
    .info-label {
        font-size: 12px;
        color: #6b7280;
        text-transform: uppercase;
        letter-spacing: 0.5px;
        margin-bottom: 5px;
    }
    .info-value {
        font-size: 18px;
        font-weight: bold;
        color: #1f2937;
    }
    .items-section {
        margin-bottom: 30px;
    }
    .item {
        border: 1px solid #e5e7eb;
        border-radius: 8px;
        padding: 20px;
        margin-bottom: 15px;
        background: #f9fafb;
    }
    .item-header {
        display: flex;
        justify-content: between;
        align-items: center;
        margin-bottom: 10px;
    }
    .item-name {
        font-size: 18px;
        font-weight: bold;
        color: #1f2937;
    }
    .item-quantity {
        background: #3b82f6;
        color: white;
        padding: 5px 12px;
        border-radius: 20px;
        font-weight: bold;
        margin-left: auto;
    }
    .item-details {
        color: #6b7280;
        margin-bottom: 5px;
    }
    .special-instructions {
        background: #fef3c7;
        border: 1px solid #f59e0b;
        border-radius: 8px;
        padding: 15px;
        margin-top: 20px;
    }
    .action-buttons {
        display: flex;
        gap: 15px;
        justify-content: center;
        margin-top: 30px;
    }
    .btn {
        padding: 12px 24px;
        border-radius: 8px;
        font-weight: bold;
        text-decoration: none;
        transition: all 0.3s ease;
        border: none;
        cursor: pointer;
    }
    .btn-primary {
        background: #3b82f6;
        color: white;
    }
    .btn-primary:hover {
        background: #2563eb;
        transform: translateY(-2px);
    }
    .btn-secondary {
        background: #6b7280;
        color: white;
    }
    .btn-secondary:hover {
        background: #4b5563;
        transform: translateY(-2px);
    }
    .btn-success {
        background: #10b981;
        color: white;
    }
    .btn-success:hover {
        background: #059669;
        transform: translateY(-2px);
    }
</style>
@endpush

@section('content')
<div class="kot-container">
    <!-- Header -->
    <div class="kot-header">
        <h1 class="text-3xl font-bold mb-2">Kitchen Order Ticket</h1>
        <p class="text-blue-100">Order #{{ $order->order_number }}</p>
    </div>

    <div class="kot-content">
        <!-- Order Information -->
        <div class="order-info">
            <div class="info-item">
                <div class="info-label">Order Time</div>
                <div class="info-value">{{ $order->created_at->format('H:i') }}</div>
            </div>
            <div class="info-item">
                <div class="info-label">Order Type</div>
                <div class="info-value">{{ ucfirst(str_replace('_', ' ', $order->order_type)) }}</div>
            </div>
            @if($order->table)
                <div class="info-item">
                    <div class="info-label">Table</div>
                    <div class="info-value">{{ $order->table->name }}</div>
                </div>
            @endif
            @if($order->customer)
                <div class="info-item">
                    <div class="info-label">Customer</div>
                    <div class="info-value">{{ $order->customer->name }}</div>
                </div>
            @endif
            @if($order->pax)
                <div class="info-item">
                    <div class="info-label">Pax</div>
                    <div class="info-value">{{ $order->pax }}</div>
                </div>
            @endif
        </div>

        <!-- Items -->
        <div class="items-section">
            <h2 class="text-xl font-bold mb-4">Order Items</h2>
            
            @foreach($order->orderItems as $item)
                <div class="item">
                    <div class="item-header">
                        <div class="item-name">{{ $item->menuItem->name }}</div>
                        <div class="item-quantity">{{ $item->quantity }}x</div>
                    </div>
                    
                    @if($item->variant_name)
                        <div class="item-details">
                            <strong>Variant:</strong> {{ $item->variant_name }}
                        </div>
                    @endif
                    
                    @if($item->addons && count($item->addons) > 0)
                        <div class="item-details">
                            <strong>Add-ons:</strong>
                            @foreach($item->addons as $addon)
                                {{ $addon['addon_name'] }} ({{ $addon['quantity'] }}x){{ !$loop->last ? ', ' : '' }}
                            @endforeach
                        </div>
                    @endif
                    
                    @if($item->notes)
                        <div class="item-details">
                            <strong>Notes:</strong> {{ $item->notes }}
                        </div>
                    @endif
                </div>
            @endforeach
        </div>

        @if($order->notes)
            <div class="special-instructions">
                <h3 class="font-bold text-amber-800 mb-2">Special Instructions:</h3>
                <p class="text-amber-700">{{ $order->notes }}</p>
            </div>
        @endif

        <!-- Action Buttons -->
        <div class="action-buttons">
            <a href="{{ route('pos.create') }}" class="btn btn-secondary">
                <i class="mdi mdi-arrow-left mr-2"></i>
                Back to POS
            </a>
            <button onclick="window.print()" class="btn btn-primary">
                <i class="mdi mdi-printer mr-2"></i>
                Print KOT
            </button>
            <a href="{{ route('pos.orders.bill', $order) }}" class="btn btn-success">
                <i class="mdi mdi-receipt mr-2"></i>
                Generate Bill
            </a>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<style>
    @media print {
        .kot-header, .action-buttons { display: none; }
        .kot-container { max-width: none; margin: 0; padding: 0; }
        .kot-content { box-shadow: none; border-radius: 0; }
        body { font-size: 12px; }
    }
</style>
@endpush
