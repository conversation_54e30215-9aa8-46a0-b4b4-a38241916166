<?php $__env->startSection('css'); ?>
<meta name="csrf-token" content="<?php echo e(csrf_token()); ?>">
<link href="<?php echo e(URL::asset('assets/plugins/sweet-alert/sweetalert.css')); ?>" rel="stylesheet">
<style>
/* Order Status Styles */
.status-pending { @apply bg-yellow-200 text-gray-800; }
.status-confirmed { @apply bg-blue-500 text-white; }
.status-preparing { @apply bg-pink-500 text-white; }
.status-ready { @apply bg-green-500 text-white; }
.status-served { @apply bg-purple-500 text-white; }
.status-completed { @apply bg-teal-500 text-white; }
.status-cancelled { @apply bg-red-500 text-white; }

/* Order Type Styles */
.type-dine_in { @apply bg-green-100 text-green-800; }
.type-takeaway { @apply bg-orange-100 text-orange-800; }
.type-delivery { @apply bg-blue-100 text-blue-800; }
.type-online { @apply bg-purple-100 text-purple-800; }

@media print {
    .no-print { display: none !important; }
    .print-card { border: none !important; box-shadow: none !important; }
}
</style>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('page-header'); ?>
<!-- breadcrumb -->
<div class="flex justify-between items-center mb-6">
    <div class="flex-1">
        <div class="flex items-center">
            <h4 class="text-2xl font-semibold text-gray-800 mb-0">تفاصيل الطلب</h4>
            <span class="text-gray-500 text-sm mr-2 mt-1">/ الطلبات / التفاصيل</span>
        </div>
    </div>
    <div class="flex items-center space-x-2 no-print">
        <a href="<?php echo e(route('orders.index')); ?>" class="inline-flex items-center justify-center w-10 h-10 bg-gray-500 hover:bg-gray-600 text-white rounded-lg transition-colors duration-200">
            <i class="mdi mdi-arrow-left"></i>
        </a>
        <a href="<?php echo e(route('orders.edit', $order->id ?? 1)); ?>" class="inline-flex items-center justify-center w-10 h-10 bg-yellow-500 hover:bg-yellow-600 text-white rounded-lg transition-colors duration-200">
            <i class="mdi mdi-pencil"></i>
        </a>
        <button onclick="window.print()" class="inline-flex items-center justify-center w-10 h-10 bg-blue-500 hover:bg-blue-600 text-white rounded-lg transition-colors duration-200">
            <i class="mdi mdi-printer"></i>
        </button>
    </div>
</div>
<!-- breadcrumb -->
<?php $__env->stopSection(); ?>

<?php $__env->startSection('content'); ?>
<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
    <!-- Order Header Card -->
    <div class="mb-6">
        <div class="bg-white rounded-lg shadow-md print-card">
            <div class="px-6 py-4 border-b border-gray-200 flex justify-between items-center">
                <h3 class="text-xl font-semibold text-gray-800">الطلب #<?php echo e($order->order_number ?? 'ORD-001'); ?></h3>
                <div class="flex items-center space-x-2">
                    <span class="px-4 py-2 rounded-md font-semibold text-sm uppercase status-<?php echo e($order->status ?? 'pending'); ?>">
                        <?php echo e($order->status_text ?? 'معلق'); ?>

                    </span>
                    <span class="px-3 py-1 rounded-md text-sm font-medium type-<?php echo e($order->order_type ?? 'dine_in'); ?>">
                        <?php echo e($order->order_type_text ?? 'تناول في المطعم'); ?>

                    </span>
                </div>
            </div>
            <div class="p-6">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <h6 class="text-sm font-medium text-gray-500 mb-3">معلومات العميل</h6>
                        <div class="space-y-2">
                            <p class="text-sm"><span class="font-semibold">الاسم:</span> <?php echo e($order->customer->name ?? 'عميل مجهول'); ?></p>
                            <p class="text-sm"><span class="font-semibold">الهاتف:</span> <?php echo e($order->customer->phone ?? 'غير محدد'); ?></p>
                            <p class="text-sm"><span class="font-semibold">البريد الإلكتروني:</span> <?php echo e($order->customer->email ?? 'غير محدد'); ?></p>
                        </div>
                    </div>
                    <div>
                        <h6 class="text-sm font-medium text-gray-500 mb-3">تفاصيل الطلب</h6>
                        <div class="space-y-2">
                            <p class="text-sm"><span class="font-semibold">الطاولة:</span> <?php echo e($order->table->name ?? 'غير محدد'); ?></p>
                            <p class="text-sm"><span class="font-semibold">تاريخ الطلب:</span> <?php echo e($order->created_at ? $order->created_at->format('Y-m-d H:i') : now()->format('Y-m-d H:i')); ?></p>
                            <p class="text-sm"><span class="font-semibold">الموظف:</span> <?php echo e($order->user->name ?? auth()->user()->name); ?></p>
                        </div>
                    </div>
                </div>
                
                <?php if($order->special_instructions ?? false): ?>
                <div class="mt-6">
                    <h6 class="text-sm font-medium text-gray-500 mb-3">ملاحظات خاصة</h6>
                    <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
                        <p class="text-blue-800"><?php echo e($order->special_instructions); ?></p>
                    </div>
                </div>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <!-- Order Items Card -->
    <div class="mb-6">
        <div class="bg-white rounded-lg shadow-md print-card">
            <div class="px-6 py-4 border-b border-gray-200">
                <h3 class="text-xl font-semibold text-gray-800">عناصر الطلب</h3>
            </div>
            <div class="p-6">
                <div class="overflow-x-auto">
                    <table class="min-w-full divide-y divide-gray-200">
                        <thead class="bg-gray-50">
                            <tr>
                                <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">العنصر</th>
                                <th class="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">السعر</th>
                                <th class="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">الكمية</th>
                                <th class="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">المجموع</th>
                                <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">ملاحظات</th>
                            </tr>
                        </thead>
                        <tbody class="bg-white divide-y divide-gray-200">
                            <?php
                                $sampleItems = [
                                    ['name' => 'برجر كلاسيك', 'price' => 15.99, 'quantity' => 2, 'notes' => 'بدون بصل'],
                                    ['name' => 'بيتزا مارجريتا', 'price' => 22.50, 'quantity' => 1, 'notes' => ''],
                                    ['name' => 'كوكا كولا', 'price' => 3.50, 'quantity' => 2, 'notes' => 'مع ثلج']
                                ];
                                $subtotal = 0;
                            ?>
                            
                            <?php $__empty_1 = true; $__currentLoopData = $order->items ?? $sampleItems; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                                <?php
                                    $itemTotal = ($item['price'] ?? $item->price ?? 0) * ($item['quantity'] ?? $item->quantity ?? 1);
                                    $subtotal += $itemTotal;
                                ?>
                                <tr class="hover:bg-gray-50">
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="text-sm font-medium text-gray-900"><?php echo e($item['name'] ?? $item->menu_item->name ?? 'عنصر غير محدد'); ?></div>
                                        <?php if(isset($item->variants) && $item->variants->count() > 0): ?>
                                            <div class="text-sm text-gray-500">
                                                <?php $__currentLoopData = $item->variants; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $variant): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                    <?php echo e($variant->name); ?><?php echo e(!$loop->last ? ', ' : ''); ?>

                                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                            </div>
                                        <?php endif; ?>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-center text-sm text-gray-900">$<?php echo e(number_format($item['price'] ?? $item->price ?? 0, 2)); ?></td>
                                    <td class="px-6 py-4 whitespace-nowrap text-center text-sm text-gray-900"><?php echo e($item['quantity'] ?? $item->quantity ?? 1); ?></td>
                                    <td class="px-6 py-4 whitespace-nowrap text-center text-sm font-medium text-gray-900">$<?php echo e(number_format($itemTotal, 2)); ?></td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500"><?php echo e($item['notes'] ?? $item->notes ?? ''); ?></td>
                                </tr>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                                <tr>
                                    <td colspan="5" class="px-6 py-4 whitespace-nowrap text-center text-sm text-gray-500">لا توجد عناصر في هذا الطلب</td>
                                </tr>
                            <?php endif; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <!-- Order Summary Card -->
    <div class="mb-6">
        <div class="max-w-md ml-auto">
            <div class="bg-white rounded-lg shadow-md print-card">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h3 class="text-xl font-semibold text-gray-800">ملخص الطلب</h3>
                </div>
                <div class="p-6">
                    <?php
                        $tax = $subtotal * 0.15; // 15% tax
                        $discount = $order->discount_amount ?? 0;
                        $total = $subtotal + $tax - $discount;
                    ?>
                    
                    <div class="space-y-3">
                        <div class="flex justify-between items-center">
                            <span class="font-semibold text-gray-700">المجموع الفرعي:</span>
                            <span class="text-gray-900">$<?php echo e(number_format($subtotal, 2)); ?></span>
                        </div>
                        <div class="flex justify-between items-center">
                            <span class="font-semibold text-gray-700">الضريبة (15%):</span>
                            <span class="text-gray-900">$<?php echo e(number_format($tax, 2)); ?></span>
                        </div>
                        <?php if($discount > 0): ?>
                        <div class="flex justify-between items-center">
                            <span class="font-semibold text-gray-700">الخصم:</span>
                            <span class="text-red-600">-$<?php echo e(number_format($discount, 2)); ?></span>
                        </div>
                        <?php endif; ?>
                        <div class="border-t border-gray-200 pt-3">
                            <div class="flex justify-between items-center">
                                <span class="text-lg font-bold text-gray-900">المجموع الكلي:</span>
                                <span class="text-lg font-bold text-gray-900">$<?php echo e(number_format($total, 2)); ?></span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Order Actions Card -->
    <div class="no-print">
        <div class="bg-white rounded-lg shadow-md print-card">
            <div class="px-6 py-4 border-b border-gray-200">
                <h3 class="text-xl font-semibold text-gray-800">إجراءات الطلب</h3>
            </div>
            <div class="p-6">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <h6 class="text-sm font-medium text-gray-700 mb-4">تحديث حالة الطلب</h6>
                        <div class="space-y-2">
                            <button class="w-full flex items-center justify-center px-4 py-2 border border-yellow-300 text-yellow-700 bg-yellow-50 hover:bg-yellow-100 rounded-lg text-sm font-medium transition-colors duration-200" onclick="updateOrderStatus('confirmed')">
                                <i class="mdi mdi-check ml-2"></i> تأكيد الطلب
                            </button>
                            <button class="w-full flex items-center justify-center px-4 py-2 border border-blue-300 text-blue-700 bg-blue-50 hover:bg-blue-100 rounded-lg text-sm font-medium transition-colors duration-200" onclick="updateOrderStatus('preparing')">
                                <i class="mdi mdi-chef-hat ml-2"></i> بدء التحضير
                            </button>
                            <button class="w-full flex items-center justify-center px-4 py-2 border border-green-300 text-green-700 bg-green-50 hover:bg-green-100 rounded-lg text-sm font-medium transition-colors duration-200" onclick="updateOrderStatus('ready')">
                                <i class="mdi mdi-bell ml-2"></i> جاهز للتقديم
                            </button>
                            <button class="w-full flex items-center justify-center px-4 py-2 border border-indigo-300 text-indigo-700 bg-indigo-50 hover:bg-indigo-100 rounded-lg text-sm font-medium transition-colors duration-200" onclick="updateOrderStatus('served')">
                                <i class="mdi mdi-silverware ml-2"></i> تم التقديم
                            </button>
                            <button class="w-full flex items-center justify-center px-4 py-2 border border-gray-300 text-gray-700 bg-gray-50 hover:bg-gray-100 rounded-lg text-sm font-medium transition-colors duration-200" onclick="updateOrderStatus('completed')">
                                <i class="mdi mdi-check-all ml-2"></i> مكتمل
                            </button>
                        </div>
                    </div>
                    <div>
                        <h6 class="text-sm font-medium text-gray-700 mb-4">إجراءات أخرى</h6>
                        <div class="space-y-2">
                            <a href="<?php echo e(route('pos.orders.kot', $order->id ?? 1)); ?>" class="w-full flex items-center justify-center px-4 py-2 bg-green-600 hover:bg-green-700 text-white rounded-lg text-sm font-medium transition-colors duration-200">
                                <i class="mdi mdi-printer ml-2"></i> طباعة KOT
                            </a>
                            <a href="<?php echo e(route('orders.edit', $order->id ?? 1)); ?>" class="w-full flex items-center justify-center px-4 py-2 bg-yellow-600 hover:bg-yellow-700 text-white rounded-lg text-sm font-medium transition-colors duration-200">
                                <i class="mdi mdi-pencil ml-2"></i> تعديل الطلب
                            </a>
                            <button class="w-full flex items-center justify-center px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg text-sm font-medium transition-colors duration-200" onclick="window.print()">
                                <i class="mdi mdi-printer ml-2"></i> طباعة الفاتورة
                            </button>
                            <button class="w-full flex items-center justify-center px-4 py-2 bg-red-600 hover:bg-red-700 text-white rounded-lg text-sm font-medium transition-colors duration-200" onclick="cancelOrder()">
                                <i class="mdi mdi-cancel ml-2"></i> إلغاء الطلب
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('js'); ?>
<script src="<?php echo e(URL::asset('assets/plugins/sweet-alert/sweetalert.min.js')); ?>"></script>

<script>
$(document).ready(function() {
    // Add CSRF token to all AJAX requests
    $.ajaxSetup({
        headers: {
            'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
        }
    });
});

// Update order status
function updateOrderStatus(status) {
    const orderId = <?php echo e($order->id ?? 1); ?>;
    
    // For confirmation, work directly without modal
    if (status === 'confirmed') {
        // Show loading indicator on the button
        const confirmBtn = $('button[onclick="updateOrderStatus(\'confirmed\')"]');
        const originalText = confirmBtn.html();
        confirmBtn.prop('disabled', true).html('<i class="mdi mdi-loading mdi-spin"></i> جاري التأكيد...');
        
        $.ajax({
            url: `/api/orders/${orderId}/confirm`,
            method: 'POST',
            success: function(response) {
                if (response.success) {
                    // Extract order number and KOT information
                    const orderNumber = response.data.order.order_number || orderId;
                    let kotNumber = null;
                    
                    // Check if KOT was created successfully
                    if (response.data.kot_orders && response.data.kot_orders.length > 0) {
                        kotNumber = response.data.kot_orders[0].kot_number || response.data.kot_orders[0].id;
                    }
                    
                    // Show toast notification
                    showOrderConfirmationToast(orderNumber, kotNumber);
                    
                    // Update the order status display without reloading
                    $('.order-status').removeClass('status-pending').addClass('status-confirmed').text('مؤكد');
                    
                    // Hide the confirm button since order is now confirmed
                    confirmBtn.closest('.btn-group-vertical').find('button[onclick="updateOrderStatus(\'confirmed\')"]').hide();
                } else {
                    showOrderErrorToast(response.message || "حدث خطأ أثناء تأكيد الطلب");
                }
                
                // Restore button state
                confirmBtn.prop('disabled', false).html(originalText);
            },
            error: function(xhr) {
                console.error('Error confirming order:', xhr);
                let errorMessage = "حدث خطأ أثناء تأكيد الطلب";
                
                if (xhr.responseJSON && xhr.responseJSON.message) {
                    errorMessage = xhr.responseJSON.message;
                }
                
                showOrderErrorToast(errorMessage);
                
                // Restore button
                confirmBtn.prop('disabled', false).html(originalText);
            }
        });
    } else {
        // For other statuses, show confirmation modal
        swal({
            title: "تأكيد التحديث",
            text: "هل أنت متأكد من تحديث حالة الطلب؟",
            type: "warning",
            showCancelButton: true,
            confirmButtonColor: "#3085d6",
            cancelButtonColor: "#d33",
            confirmButtonText: "نعم، حدث!",
            cancelButtonText: "إلغاء"
        }, function() {
            $.ajax({
                url: `/api/orders/${orderId}/status`,
                method: 'PUT',
                data: { status: status },
                success: function(response) {
                    swal("تم التحديث!", "تم تحديث حالة الطلب بنجاح", "success");
                    location.reload();
                },
                error: function(xhr) {
                    swal("خطأ!", "حدث خطأ أثناء تحديث حالة الطلب", "error");
                }
            });
        });
    }
}

// Cancel order
function cancelOrder() {
    const orderId = <?php echo e($order->id ?? 1); ?>;
    
    swal({
        title: "إلغاء الطلب",
        text: "هل أنت متأكد من إلغاء هذا الطلب؟ لا يمكن التراجع عن هذا الإجراء!",
        type: "warning",
        showCancelButton: true,
        confirmButtonColor: "#d33",
        cancelButtonColor: "#3085d6",
        confirmButtonText: "نعم، ألغ الطلب!",
        cancelButtonText: "تراجع"
    }, function() {
        updateOrderStatus('cancelled');
    });
}
</script>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.master', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH D:\my dehive work\resturant-pos - Copy\epis - Copy\modules\Orders\Providers/../resources/views/show.blade.php ENDPATH**/ ?>