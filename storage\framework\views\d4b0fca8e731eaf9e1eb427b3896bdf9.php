<?php $__env->startSection('css'); ?>
<!-- Select2 CSS -->
<link href="<?php echo e(URL::asset('assets/plugins/select2/css/select2.min.css')); ?>" rel="stylesheet">
<!-- Sweet Alert CSS -->
<link href="<?php echo e(URL::asset('assets/plugins/sweet-alert/sweetalert.css')); ?>" rel="stylesheet">
<?php $__env->stopSection(); ?>

<?php $__env->startSection('page-header'); ?>
<!-- breadcrumb -->
<div class="flex justify-between items-center mb-6">
    <div>
        <h4 class="text-2xl font-bold text-gray-800 mb-1">طلبات النادل</h4>
        <nav class="text-sm text-gray-600">
            <span>طلبات الخدمة</span>
        </nav>
    </div>
    <div class="flex items-center space-x-3">
        <button type="button" class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg transition-colors duration-200 flex items-center" id="add-request-btn">
            <i class="mdi mdi-plus mr-2"></i>
            إضافة طلب
        </button>
        <button type="button" class="bg-yellow-500 hover:bg-yellow-600 text-white px-4 py-2 rounded-lg transition-colors duration-200 flex items-center" id="refresh-requests-btn">
            <i class="mdi mdi-refresh mr-2"></i>
            تحديث
        </button>
        <div class="flex bg-gray-100 rounded-lg p-1">
            <button type="button" class="bg-blue-500 text-white px-4 py-2 rounded-md transition-colors duration-200 flex items-center" id="cards-view-btn">
                <i class="mdi mdi-view-grid mr-2"></i>
                عرض البطاقات
            </button>
            <button type="button" class="text-gray-600 hover:text-gray-800 px-4 py-2 rounded-md transition-colors duration-200 flex items-center" id="table-view-btn">
                <i class="mdi mdi-view-list mr-2"></i>
                عرض الجدول
            </button>
        </div>
    </div>
</div>
<!-- breadcrumb -->
<?php $__env->stopSection(); ?>

<?php $__env->startSection('content'); ?>
<!-- Main Content -->
<div class="bg-white rounded-lg shadow-sm border border-gray-200">
    <div class="p-6 border-b border-gray-200">
        <div class="flex justify-between items-center">
            <div>
                <h4 class="text-xl font-semibold text-gray-800">قائمة طلبات النادل</h4>
                <p class="text-sm text-gray-600 mt-1">إدارة جميع طلبات الخدمة من الطاولات</p>
            </div>
            <i class="mdi mdi-dots-horizontal text-gray-400"></i>
        </div>
    </div>
    
    <div class="p-6">
        <!-- Filter Section -->
        <div class="bg-gray-50 rounded-lg p-6 mb-6">
            <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
                <div>
                    <label for="status_filter" class="block text-sm font-medium text-gray-700 mb-2">حالة الطلب</label>
                    <select class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent select2" id="status_filter">
                        <option value="">جميع الحالات</option>
                        <option value="pending">في الانتظار</option>
                        <option value="completed">مكتمل</option>
                        <option value="cancelled">ملغي</option>
                    </select>
                </div>
                <div>
                    <label for="waiter_filter" class="block text-sm font-medium text-gray-700 mb-2">النادل</label>
                    <select class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent select2" id="waiter_filter">
                        <option value="">جميع النوادل</option>
                    </select>
                </div>
                <div>
                    <label for="table_filter" class="block text-sm font-medium text-gray-700 mb-2">الطاولة</label>
                    <select class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent select2" id="table_filter">
                        <option value="">جميع الطاولات</option>
                    </select>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">&nbsp;</label>
                    <div class="flex space-x-2">
                        <button type="button" class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-md transition-colors duration-200" id="apply-filters-btn">تطبيق الفلاتر</button>
                        <button type="button" class="bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded-md transition-colors duration-200" id="clear-filters-btn">مسح الفلاتر</button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Cards View -->
        <div id="cards-view" class="min-h-96">
            <div class="flex justify-center items-center h-48" id="loading-spinner">
                <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500"></div>
                <span class="mr-3 text-gray-600">جاري التحميل...</span>
            </div>
            <div class="flex flex-wrap -mx-3" id="cards-container">
                <!-- Cards will be loaded here -->
            </div>
            <div class="flex justify-center mt-6" id="pagination-container">
                <!-- Pagination will be loaded here -->
            </div>
        </div>

        <!-- Table View (Hidden by default) -->
        <div id="table-view" class="hidden">
            <div class="overflow-x-auto">
                <table id="waiter-requests-table" class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">#</th>
                            <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">الطاولة</th>
                            <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">المنطقة</th>
                            <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">الحالة</th>
                            <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">وقت الطلب</th>
                            <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">وقت الاستجابة</th>
                            <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>
<!-- Main Content End -->

<!-- Add/Edit Waiter Request Modal -->
<div class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden" id="waiterRequestModal">
    <div class="relative top-20 mx-auto p-5 border w-11/12 md:w-3/4 lg:w-1/2 shadow-lg rounded-md bg-white">
        <div class="flex justify-between items-center pb-4 border-b border-gray-200">
            <h3 class="text-lg font-semibold text-gray-900" id="waiterRequestModalLabel">إضافة طلب خدمة جديد</h3>
            <button type="button" class="text-gray-400 hover:text-gray-600" onclick="closeModal('waiterRequestModal')">
                <i class="fas fa-times text-xl"></i>
            </button>
        </div>
        
        <form id="waiterRequestForm" class="mt-4">
            <input type="hidden" id="request_id" name="request_id">
            
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                <div>
                    <label for="table_id" class="block text-sm font-medium text-gray-700 mb-2">الطاولة <span class="text-red-500">*</span></label>
                    <select class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent select2" id="table_id" name="table_id" required>
                        <option value="">اختر الطاولة</option>
                    </select>
                    <div class="text-red-500 text-sm mt-1 hidden" id="table_id_error"></div>
                </div>
                <div>
                    <label for="waiter_id" class="block text-sm font-medium text-gray-700 mb-2">النادل</label>
                    <select class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent select2" id="waiter_id" name="waiter_id">
                        <option value="">اختر النادل</option>
                    </select>
                    <div class="text-red-500 text-sm mt-1 hidden" id="waiter_id_error"></div>
                </div>
            </div>
            
            <div class="mb-4">
                <label for="status" class="block text-sm font-medium text-gray-700 mb-2">الحالة <span class="text-red-500">*</span></label>
                <select class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent" id="status" name="status" required>
                    <option value="pending">في الانتظار</option>
                    <option value="completed">مكتمل</option>
                    <option value="cancelled">ملغي</option>
                </select>
                <div class="text-red-500 text-sm mt-1 hidden" id="status_error"></div>
            </div>
            
            <div class="flex justify-end space-x-3 pt-4 border-t border-gray-200">
                <button type="button" class="bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded-md transition-colors duration-200" onclick="closeModal('waiterRequestModal')">إلغاء</button>
                <button type="submit" class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-md transition-colors duration-200" id="save-request-btn">حفظ</button>
            </div>
        </form>
    </div>
</div>

<!-- Show Waiter Request Modal -->
<div class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden" id="showWaiterRequestModal">
    <div class="relative top-20 mx-auto p-5 border w-11/12 md:w-3/4 lg:w-1/2 shadow-lg rounded-md bg-white">
        <div class="flex justify-between items-center pb-4 border-b border-gray-200">
            <h3 class="text-lg font-semibold text-gray-900" id="showWaiterRequestModalLabel">تفاصيل طلب الخدمة</h3>
            <button type="button" class="text-gray-400 hover:text-gray-600" onclick="closeModal('showWaiterRequestModal')">
                <i class="fas fa-times text-xl"></i>
            </button>
        </div>
        
        <div class="mt-4">
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">الطاولة:</label>
                    <p class="text-gray-900" id="show_table"></p>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">المنطقة:</label>
                    <p class="text-gray-900" id="show_area"></p>
                </div>
            </div>
            
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">النادل:</label>
                    <p class="text-gray-900" id="show_waiter"></p>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">الحالة:</label>
                    <p class="text-gray-900" id="show_status"></p>
                </div>
            </div>
            
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">وقت الطلب:</label>
                    <p class="text-gray-900" id="show_created_at"></p>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">وقت الاستجابة:</label>
                    <p class="text-gray-900" id="show_response_time"></p>
                </div>
            </div>
            
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">تاريخ الإنشاء:</label>
                    <p class="text-gray-900" id="show_request_created_at"></p>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">تاريخ التحديث:</label>
                    <p class="text-gray-900" id="show_updated_at"></p>
                </div>
            </div>
        </div>
        
        <div class="flex justify-end pt-4 border-t border-gray-200">
            <button type="button" class="bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded-md transition-colors duration-200" onclick="closeModal('showWaiterRequestModal')">إغلاق</button>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('js'); ?>
<!-- Select2 JS -->
<script src="<?php echo e(URL::asset('assets/plugins/select2/js/select2.min.js')); ?>"></script>
<!-- Sweet Alert JS -->
<script src="<?php echo e(URL::asset('assets/plugins/sweet-alert/sweetalert.min.js')); ?>"></script>
<!-- DataTables JS (for table view) -->
<script src="<?php echo e(URL::asset('assets/plugins/datatable/js/jquery.dataTables.min.js')); ?>"></script>
<script src="<?php echo e(URL::asset('assets/plugins/datatable/js/dataTables.bootstrap4.js')); ?>"></script>

<script>
$(document).ready(function() {
    // Add CSRF token to all AJAX requests
    $.ajaxSetup({
        headers: {
            'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
        }
    });

    // Initialize Select2
    $('.select2').select2({
        width: '100%'
    });

    // Load initial data for dropdowns
    loadTables();
    loadWaiters();

    // Current view state
    let currentView = 'cards';
    let currentPage = 1;
    let table = null;

    // Initialize cards view
    loadCardsData();

    // View toggle functionality
    $('#cards-view-btn').click(function() {
        currentView = 'cards';
        $('#cards-view').show();
        $('#table-view').hide();
        $(this).removeClass('btn-outline-primary').addClass('btn-primary');
        $('#table-view-btn').removeClass('btn-primary').addClass('btn-outline-primary');
        loadCardsData();
    });

    $('#table-view-btn').click(function() {
        currentView = 'table';
        $('#cards-view').hide();
        $('#table-view').show();
        $(this).removeClass('btn-outline-primary').addClass('btn-primary');
        $('#cards-view-btn').removeClass('btn-primary').addClass('btn-outline-primary');
        initializeDataTable();
    });

    // Load Cards Data
    function loadCardsData(page = 1) {
        $('#loading-spinner').show();
        $('#cards-container').empty();
        $('#pagination-container').empty();

        $.ajax({
            url: '<?php echo e(route("waiter-requests.cards")); ?>',
            type: 'GET',
            data: {
                page: page,
                status: $('#status_filter').val(),
                waiter_id: $('#waiter_filter').val(),
                table_id: $('#table_filter').val()
            },
            success: function(response) {
                $('#loading-spinner').hide();
                
                if (response.data && response.data.length > 0) {
                    displayCards(response.data);
                    displayPagination(response.pagination);
                } else {
                    displayEmptyState();
                }
            },
            error: function(xhr) {
                $('#loading-spinner').hide();
                console.error('Error loading cards:', xhr);
                swal('خطأ!', 'حدث خطأ أثناء تحميل البيانات', 'error');
            }
        });
    }

    // Display Cards
    function displayCards(requests) {
        let cardsHtml = '';
        
        requests.forEach(function(request) {
            const statusClass = request.status;
            const statusBadge = getStatusBadgeClass(request.status);
            const tableName = request.table ? (request.table.table_name || 'طاولة ' + request.table.table_number) : '-';
            const areaName = request.table && request.table.area ? request.table.area.name : '-';
            const waiterName = request.waiter ? request.waiter.name : 'غير محدد';
            const createdAt = new Date(request.created_at).toLocaleString('ar-SA');
            const responseTime = request.response_time ? new Date(request.response_time).toLocaleString('ar-SA') : 'لم يتم الرد بعد';
            const requestType = request.request_type || 'خدمة عامة';
            const notes = request.notes || '-';

            cardsHtml += `
                <div class="w-full sm:w-1/2 lg:w-1/3 xl:w-1/4 p-3">
                    <div class="bg-white rounded-lg shadow-md hover:shadow-lg transition-shadow duration-300 border-l-4 ${getStatusBorderClass(request.status)}">
                        <div class="p-4 bg-gray-50 border-b border-gray-200">
                            <div class="flex justify-between items-center">
                                <h6 class="font-semibold text-lg mb-0 text-gray-800">
                                    <i class="mdi mdi-table-furniture text-blue-600"></i> ${tableName}
                                </h6>
                                ${statusBadge}
                            </div>
                            <small class="text-gray-600">
                                <i class="mdi mdi-map-marker text-gray-500"></i> ${areaName}
                            </small>
                        </div>
                        <div class="p-4">
                            <div class="grid grid-cols-2 gap-4 mb-3">
                                <div>
                                    <small class="text-gray-500">النادل:</small>
                                    <div class="font-semibold text-gray-900">${waiterName}</div>
                                </div>
                                <div>
                                    <small class="text-gray-500">نوع الطلب:</small>
                                    <div class="font-semibold text-gray-900">${requestType}</div>
                                </div>
                            </div>
                            
                            ${notes !== '-' ? `
                            <div class="mb-3">
                                <small class="text-gray-500">ملاحظات:</small>
                                <div class="text-gray-900 truncate" title="${notes}">${notes}</div>
                            </div>
                            ` : ''}
                            
                            <div class="mb-4">
                                <div class="flex justify-between items-center">
                                    <span class="text-sm text-gray-600"><i class="mdi mdi-clock-outline"></i> ${createdAt}</span>
                                </div>
                                ${request.response_time ? `
                                <div class="mt-1">
                                    <small class="text-green-600">
                                        <i class="mdi mdi-check-circle"></i> استجيب في: ${responseTime}
                                    </small>
                                </div>
                                ` : ''}
                            </div>
                            
                            <div class="flex justify-center space-x-2">
                                <button type="button" class="bg-blue-500 hover:bg-blue-600 text-white px-3 py-1 rounded text-sm show-request" data-id="${request.id}" title="عرض">
                                    <i class="fas fa-eye"></i>
                                </button>
                                
                                ${request.status === 'pending' ? `
                                <button type="button" class="bg-green-500 hover:bg-green-600 text-white px-3 py-1 rounded text-sm complete-request" data-id="${request.id}" title="إكمال">
                                    <i class="fas fa-check"></i>
                                </button>
                                <button type="button" class="bg-yellow-500 hover:bg-yellow-600 text-white px-3 py-1 rounded text-sm edit-request" data-id="${request.id}" title="تعديل">
                                    <i class="fas fa-edit"></i>
                                </button>
                                <button type="button" class="bg-gray-500 hover:bg-gray-600 text-white px-3 py-1 rounded text-sm cancel-request" data-id="${request.id}" title="إلغاء">
                                    <i class="fas fa-times"></i>
                                </button>
                                ` : ''}
                                
                                <button type="button" class="bg-red-500 hover:bg-red-600 text-white px-3 py-1 rounded text-sm delete-request" data-id="${request.id}" title="حذف">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            `;
        });
        
        $('#cards-container').html(cardsHtml);
    }

    // Display Empty State
    function displayEmptyState() {
        const emptyHtml = `
            <div class="col-span-full">
                <div class="text-center py-12">
                    <i class="mdi mdi-table-furniture text-6xl text-gray-400 mb-4"></i>
                    <h5 class="text-xl font-semibold text-gray-600 mb-2">لا توجد طلبات خدمة</h5>
                    <p class="text-gray-500">لم يتم العثور على أي طلبات خدمة مطابقة للفلاتر المحددة</p>
                </div>
            </div>
        `;
        $('#cards-container').html(emptyHtml);
    }

    // Display Pagination
    function displayPagination(pagination) {
        if (pagination.last_page <= 1) {
            return;
        }

        let paginationHtml = '<nav aria-label="Page navigation" class="flex justify-center mt-6"><ul class="flex space-x-1">';
        
        // Previous button
        if (pagination.current_page > 1) {
            paginationHtml += `<li>
                <a class="px-3 py-2 bg-white border border-gray-300 text-gray-700 hover:bg-gray-50 rounded-md cursor-pointer" data-page="${pagination.current_page - 1}">السابق</a>
            </li>`;
        }
        
        // Page numbers
        for (let i = 1; i <= pagination.last_page; i++) {
            const activeClass = i === pagination.current_page ? 'bg-blue-500 text-white border-blue-500' : 'bg-white text-gray-700 border-gray-300 hover:bg-gray-50';
            paginationHtml += `<li>
                <a class="px-3 py-2 border rounded-md cursor-pointer ${activeClass}" data-page="${i}">${i}</a>
            </li>`;
        }
        
        // Next button
        if (pagination.current_page < pagination.last_page) {
            paginationHtml += `<li>
                <a class="px-3 py-2 bg-white border border-gray-300 text-gray-700 hover:bg-gray-50 rounded-md cursor-pointer" data-page="${pagination.current_page + 1}">التالي</a>
            </li>`;
        }
        
        paginationHtml += '</ul></nav>';
        
        $('#pagination-container').html(paginationHtml);
        
        // Pagination click events
        $('[data-page]').click(function(e) {
            e.preventDefault();
            const page = $(this).data('page');
            if (page) {
                currentPage = page;
                loadCardsData(page);
            }
        });
    }

    // Initialize DataTable for table view
    function initializeDataTable() {
        if (table) {
            table.destroy();
        }
        
        table = $('#waiter-requests-table').DataTable({
            processing: true,
            serverSide: true,
            ajax: {
                url: '<?php echo e(route("waiter-requests.data")); ?>',
                data: function(d) {
                    d.status = $('#status_filter').val();
                    d.waiter_id = $('#waiter_filter').val();
                    d.table_id = $('#table_filter').val();
                }
            },
            columns: [
                { data: 'DT_RowIndex', name: 'DT_RowIndex', orderable: false, searchable: false },
                { data: 'table_name', name: 'table_name' },
                { data: 'area_name', name: 'area_name' },
                { data: 'status', name: 'status' },
                { data: 'created_at', name: 'created_at' },
                { data: 'response_time', name: 'response_time' },
                { data: 'action', name: 'action', orderable: false, searchable: false }
            ],
            language: {
                url: '//cdn.datatables.net/plug-ins/1.10.25/i18n/Arabic.json'
            },
            order: [[4, 'desc']]
        });
    }

    // Load Tables
    function loadTables() {
        $.ajax({
            url: '<?php echo e(route("tables.list")); ?>',
            type: 'GET',
            success: function(response) {
                var tableSelect = $('#table_id, #table_filter');
                tableSelect.find('option:not(:first)').remove();
                
                if (response && Array.isArray(response)) {
                    $.each(response, function(index, table) {
                        var option = '<option value="' + table.id + '">طاولة ' + table.table_number + ' - ' + (table.area ? table.area.name : 'غير محدد') + '</option>';
                        tableSelect.append(option);
                    });
                }
            },
            error: function(xhr) {
                console.error('Error loading tables:', xhr);
            }
        });
    }

    // Load Waiters
    function loadWaiters() {
        $.ajax({
            url: '<?php echo e(route("waiters.list")); ?>',
            type: 'GET',
            success: function(response) {
                var waiterSelect = $('#waiter_id, #waiter_filter');
                waiterSelect.find('option:not(:first)').remove();
                
                if (response && Array.isArray(response)) {
                    $.each(response, function(index, waiter) {
                        var option = '<option value="' + waiter.id + '">' + waiter.name + '</option>';
                        waiterSelect.append(option);
                    });
                }
            },
            error: function(xhr) {
                console.error('Error loading waiters:', xhr);
            }
        });
    }

    // Apply Filters
    $('#apply-filters-btn').click(function() {
        currentPage = 1;
        if (currentView === 'cards') {
            loadCardsData();
        } else {
            table.ajax.reload();
        }
    });

    // Clear Filters
    $('#clear-filters-btn').click(function() {
        $('#status_filter, #waiter_filter, #table_filter').val('').trigger('change');
        currentPage = 1;
        if (currentView === 'cards') {
            loadCardsData();
        } else {
            table.ajax.reload();
        }
    });

    // Refresh Requests
    $('#refresh-requests-btn').click(function() {
        if (currentView === 'cards') {
            loadCardsData(currentPage);
        } else {
            table.ajax.reload();
        }
        swal("تم التحديث!", "تم تحديث قائمة الطلبات", "success");
    });

    // Helper function to get status badge class for Tailwind
    function getStatusBadgeClass(status) {
        switch(status) {
            case 'pending':
                return '<span class="bg-yellow-100 text-yellow-800 text-xs font-medium px-2.5 py-0.5 rounded">في الانتظار</span>';
            case 'completed':
                return '<span class="bg-green-100 text-green-800 text-xs font-medium px-2.5 py-0.5 rounded">مكتمل</span>';
            case 'cancelled':
                return '<span class="bg-red-100 text-red-800 text-xs font-medium px-2.5 py-0.5 rounded">ملغي</span>';
            default:
                return '<span class="bg-gray-100 text-gray-800 text-xs font-medium px-2.5 py-0.5 rounded">' + status + '</span>';
        }
    }

    // Helper function to get status border class
    function getStatusBorderClass(status) {
        switch(status) {
            case 'pending':
                return 'border-yellow-400';
            case 'completed':
                return 'border-green-400';
            case 'cancelled':
                return 'border-red-400';
            default:
                return 'border-gray-400';
        }
    }

    // Helper function to get status header class (no longer used with new design)
    function getStatusHeaderClass(status) {
        return 'bg-gray-50'; // Simple background for all statuses
    }

    // Helper function to get status badge
    function getStatusBadge(status) {
        var badgeClass = 'badge-secondary';
        var statusText = status;
        
        switch(status) {
            case 'pending':
                badgeClass = 'badge-warning';
                statusText = 'في الانتظار';
                break;
            case 'completed':
                badgeClass = 'badge-success';
                statusText = 'مكتمل';
                break;
            case 'cancelled':
                badgeClass = 'badge-danger';
                statusText = 'ملغي';
                break;
        }
        
        return '<span class="badge ' + badgeClass + ' status-badge">' + statusText + '</span>';
    }

    // Auto-refresh every 30 seconds for real-time updates
    setInterval(function() {
        if (currentView === 'cards') {
            loadCardsData(currentPage);
        } else if (table) {
            table.ajax.reload(null, false);
        }
    }, 30000);

    // Close Modal Function for Tailwind CSS
    function closeModal(modalId) {
        $('#' + modalId).addClass('hidden');
    }

    // Show Modal Function for Tailwind CSS
    function showModal(modalId) {
        $('#' + modalId).removeClass('hidden');
    }

    // Add Request Button
    $('#add-request-btn').click(function() {
        $('#waiterRequestForm')[0].reset();
        $('#request_id').val('');
        $('#waiterRequestModalLabel').text('إضافة طلب خدمة جديد');
        $('.text-red-500').addClass('hidden');
        $('.select2').trigger('change');
        showModal('waiterRequestModal');
    });

    // Edit Request Button
    $(document).on('click', '.edit-request', function() {
        var id = $(this).data('id');
        
        $.ajax({
            url: '<?php echo e(url("/waiter-requests")); ?>/' + id,
            type: 'GET',
            success: function(response) {
                var request = response.data;
                $('#request_id').val(request.id);
                $('#table_id').val(request.table_id).trigger('change');
                $('#waiter_id').val(request.waiter_id).trigger('change');
                $('#status').val(request.status);
                $('#waiterRequestModalLabel').text('تعديل طلب الخدمة');
                $('.text-red-500').addClass('hidden');
                showModal('waiterRequestModal');
            },
            error: function(xhr) {
                swal('خطأ!', 'حدث خطأ أثناء جلب بيانات الطلب', 'error');
            }
        });
    });

    // Show Request Button
    $(document).on('click', '.show-request', function() {
        var id = $(this).data('id');
        
        $.ajax({
            url: '<?php echo e(url("/waiter-requests")); ?>/' + id,
            type: 'GET',
            success: function(response) {
                var request = response.data;
                $('#show_table').text(request.table ? 'طاولة ' + request.table.table_number : '-');
                $('#show_area').text(request.table && request.table.area ? request.table.area.name : '-');
                $('#show_waiter').text(request.waiter ? request.waiter.name : 'غير محدد');
                $('#show_status').html(getStatusBadgeClass(request.status));
                $('#show_created_at').text(new Date(request.created_at).toLocaleString('ar-SA'));
                $('#show_response_time').text(request.response_time ? new Date(request.response_time).toLocaleString('ar-SA') : 'لم يتم الرد بعد');
                $('#show_request_created_at').text(new Date(request.created_at).toLocaleDateString('ar-SA'));
                $('#show_updated_at').text(new Date(request.updated_at).toLocaleDateString('ar-SA'));
                showModal('showWaiterRequestModal');
            },
            error: function(xhr) {
                swal('خطأ!', 'حدث خطأ أثناء جلب بيانات الطلب', 'error');
            }
        });
    });

    // Complete Request Button
    $(document).on('click', '.complete-request', function() {
        var id = $(this).data('id');
        
        swal({
            title: "تأكيد إكمال الطلب",
            text: "هل تريد تأكيد إكمال هذا الطلب؟",
            type: "info",
            showCancelButton: true,
            confirmButtonColor: "#28a745",
            confirmButtonText: "نعم، أكمل الطلب",
            cancelButtonText: "إلغاء",
            closeOnConfirm: false
        }, function() {
            $.ajax({
                url: '<?php echo e(url("/waiter-requests")); ?>/' + id + '/complete',
                type: 'PATCH',
                headers: {
                    'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                },
                success: function(response) {
                    swal("تم الإكمال!", "تم إكمال الطلب بنجاح.", "success");
                    if (currentView === 'cards') {
                        loadCardsData(currentPage);
                    } else {
                        table.ajax.reload();
                    }
                },
                error: function(xhr) {
                    swal("خطأ!", "حدث خطأ أثناء إكمال الطلب", "error");
                }
            });
        });
    });

    // Cancel Request Button
    $(document).on('click', '.cancel-request', function() {
        var id = $(this).data('id');
        
        swal({
            title: "تأكيد إلغاء الطلب",
            text: "هل تريد إلغاء هذا الطلب؟",
            type: "warning",
            showCancelButton: true,
            confirmButtonColor: "#dc3545",
            confirmButtonText: "نعم، ألغ الطلب",
            cancelButtonText: "إلغاء",
            closeOnConfirm: false
        }, function() {
            $.ajax({
                url: '<?php echo e(url("/waiter-requests")); ?>/' + id + '/cancel',
                type: 'PATCH',
                headers: {
                    'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                },
                success: function(response) {
                    swal("تم الإلغاء!", "تم إلغاء الطلب بنجاح.", "success");
                    if (currentView === 'cards') {
                        loadCardsData(currentPage);
                    } else {
                        table.ajax.reload();
                    }
                },
                error: function(xhr) {
                    swal("خطأ!", "حدث خطأ أثناء إلغاء الطلب", "error");
                }
            });
        });
    });

    // Delete Request Button
    $(document).on('click', '.delete-request', function() {
        var id = $(this).data('id');
        
        swal({
            title: "هل أنت متأكد؟",
            text: "لن تتمكن من التراجع عن هذا الإجراء!",
            type: "warning",
            showCancelButton: true,
            confirmButtonColor: "#DD6B55",
            confirmButtonText: "نعم، احذف!",
            cancelButtonText: "إلغاء",
            closeOnConfirm: false
        }, function() {
            $.ajax({
                url: '<?php echo e(url("/waiter-requests")); ?>/' + id,
                type: 'DELETE',
                success: function(response) {
                    swal("تم الحذف!", "تم حذف الطلب بنجاح.", "success");
                    if (currentView === 'cards') {
                        loadCardsData(currentPage);
                    } else {
                        table.ajax.reload();
                    }
                },
                error: function(xhr) {
                    swal("خطأ!", "حدث خطأ أثناء حذف الطلب", "error");
                }
            });
        });
    });

    // Save Request Form
    $('#waiterRequestForm').submit(function(e) {
        e.preventDefault();
        
        var formData = new FormData(this);
        var id = $('#request_id').val();
        var url = id ? '<?php echo e(url("/waiter-requests")); ?>/' + id : '<?php echo e(route("waiter-requests.store")); ?>';
        var method = id ? 'PUT' : 'POST';
        
        if (method === 'PUT') {
            formData.append('_method', 'PUT');
        }
        
        $.ajax({
            url: url,
            type: 'POST',
            data: formData,
            processData: false,
            contentType: false,
            success: function(response) {
                closeModal('waiterRequestModal');
                swal("نجح!", id ? "تم تحديث الطلب بنجاح" : "تم إضافة الطلب بنجاح", "success");
                if (currentView === 'cards') {
                    loadCardsData(currentPage);
                } else {
                    table.ajax.reload();
                }
            },
            error: function(xhr) {
                if (xhr.status === 422) {
                    var errors = xhr.responseJSON.errors;
                    $('.text-red-500').addClass('hidden');
                    
                    $.each(errors, function(field, messages) {
                        $('#' + field + '_error').removeClass('hidden').text(messages[0]);
                    });
                } else {
                    swal("خطأ!", "حدث خطأ أثناء حفظ الطلب", "error");
                }
            }
        });
    });

    // Auto-refresh every 30 seconds for real-time updates
    setInterval(function() {
        if (currentView === 'cards') {
            loadCardsData(currentPage);
        } else if (table) {
            table.ajax.reload(null, false); // false to keep current page
        }
    }, 30000);
});
</script>
<?php $__env->stopSection(); ?>
<?php echo $__env->make('layouts.master', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH D:\my dehive work\resturant-pos - Copy\epis - Copy\modules\Reservation\Providers/../resources/views/waiter-requests.blade.php ENDPATH**/ ?>