<!-- Sidebar -->
<aside id="sidebar" class="fixed inset-y-0 left-0 z-50 w-64 bg-white shadow-lg transform sidebar-transition -translate-x-full md:translate-x-0 border-r border-gray-200">
    <!-- Sidebar header -->
    <div class="flex items-center justify-between h-16 px-4 border-b border-gray-200">
        <div class="flex items-center space-x-2">
            <div class="w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center">
                <i class="fas fa-utensils text-white text-sm"></i>
            </div>
            <span class="text-lg font-semibold text-gray-900">Restaurant POS</span>
        </div>
        <button id="sidebar-close" class="md:hidden p-1 rounded-md text-gray-400 hover:text-gray-600 hover:bg-gray-100">
            <i class="fas fa-times text-lg"></i>
        </button>
    </div>
    
    <!-- Branch Selector -->
    @php
        $currentUser = auth()->user();
        $currentBranch = $currentUser->branch;
        $availableBranches = \App\Models\Branch::where('tenant_id', $currentUser->tenant_id)
            ->where('status', 'active')
            ->orderBy('name')
            ->get();
    @endphp
    
    @if($availableBranches->count() > 1)
    <div class="px-4 py-3 border-b border-gray-200">
        <div class="relative">
            <label class="block text-xs font-medium text-gray-500 mb-1">Current Branch</label>
            <div class="relative">
                <button type="button" id="branchDropdownButton" class="w-full flex items-center justify-between px-3 py-2 text-sm bg-gray-50 border border-gray-200 rounded-md hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                    <div class="flex items-center">
                        <i class="fas fa-building text-gray-400 mr-2"></i>
                        <span class="text-gray-900 font-medium">{{ $currentBranch ? $currentBranch->name : 'No Branch' }}</span>
                    </div>
                    <i class="fas fa-chevron-down text-gray-400 text-xs"></i>
                </button>
                
                <!-- Dropdown Menu -->
                <div id="branchDropdownMenu" class="hidden absolute z-50 w-full mt-1 bg-white border border-gray-200 rounded-md shadow-lg max-h-60 overflow-y-auto">
                    @foreach($availableBranches as $branch)
                        <form method="POST" action="{{ route('branch.switch', $branch) }}" class="inline-block w-full">
                            @csrf
                            <button type="submit" class="w-full flex items-center px-3 py-2 text-sm text-gray-700 hover:bg-gray-100 {{ $currentBranch && $currentBranch->id === $branch->id ? 'bg-blue-50 text-blue-700' : '' }}">
                                <i class="fas fa-building text-gray-400 mr-2"></i>
                                <div class="flex-1 text-left">
                                    <div class="font-medium">{{ $branch->name }}</div>
                                    @if($branch->code)
                                        <div class="text-xs text-gray-500">{{ $branch->code }}</div>
                                    @endif
                                </div>
                                @if($currentBranch && $currentBranch->id === $branch->id)
                                    <i class="fas fa-check text-blue-600 ml-2"></i>
                                @endif
                            </button>
                        </form>
                    @endforeach
                </div>
            </div>
        </div>
    </div>
    @endif
    
    <!-- Navigation -->
    <nav class="mt-4 px-4 pb-20 overflow-y-auto h-full sidebar-scrollbar-hidden">
        <ul class="space-y-2">
            <!-- Dashboard -->
            <li>
                <a href="/dashboard" class="flex items-center px-3 py-2 text-sm font-medium rounded-md text-gray-700 hover:text-gray-900 hover:bg-gray-100 {{ request()->routeIs('dashboard') ? 'bg-blue-50 text-blue-700 border-r-2 border-blue-600' : '' }}">
                    <i class="fas fa-tachometer-alt mr-3 text-lg"></i>
                    Dashboard
                </a>
            </li>
            
            <!-- POS System -->
            <li>
                <div class="relative">
                    <button class="sidebar-dropdown-toggle flex items-center w-full px-3 py-2 text-sm font-medium rounded-md text-gray-700 hover:text-gray-900 hover:bg-gray-100" data-target="pos-menu">
                        <i class="fas fa-cash-register mr-3 text-lg"></i>
                        POS System
                        <i class="fas fa-chevron-down ml-auto text-xs transition-transform duration-200"></i>
                    </button>
                    <ul id="pos-menu" class="hidden mt-1 space-y-1 pl-6">
                        <li>
                            <a href="{{ route('pos.index') }}" class="flex items-center px-3 py-2 text-sm rounded-md text-gray-600 hover:text-gray-900 hover:bg-gray-100">
                                <i class="fas fa-desktop mr-2"></i>
                                POS Panel
                            </a>
                        </li>
                        <li>
                            <a href="{{ route('pos.create') }}" class="flex items-center px-3 py-2 text-sm rounded-md text-gray-600 hover:text-gray-900 hover:bg-gray-100">
                                <i class="fas fa-plus-circle mr-2"></i>
                                New Order
                            </a>
                        </li>
                    </ul>
                </div>
            </li>
            
            <!-- Order Management -->
            <li>
                <div class="relative">
                    <button class="sidebar-dropdown-toggle flex items-center w-full px-3 py-2 text-sm font-medium rounded-md text-gray-700 hover:text-gray-900 hover:bg-gray-100" data-target="orders-menu">
                        <i class="fas fa-shopping-cart mr-3 text-lg"></i>
                        Order Management
                        <i class="fas fa-chevron-down ml-auto text-xs transition-transform duration-200"></i>
                    </button>
                    <ul id="orders-menu" class="hidden mt-1 space-y-1 pl-6">
                        <li>
                            <a href="{{ route('orders.index') }}" class="flex items-center px-3 py-2 text-sm rounded-md text-gray-600 hover:text-gray-900 hover:bg-gray-100">
                                <i class="fas fa-list mr-2"></i>
                                All Orders
                            </a>
                        </li>
                    </ul>
                </div>
            </li>
            
            <!-- Kitchen Management -->
            <li>
                <div class="relative">
                    <button class="sidebar-dropdown-toggle flex items-center w-full px-3 py-2 text-sm font-medium rounded-md text-gray-700 hover:text-gray-900 hover:bg-gray-100" data-target="kitchen-menu">
                        <i class="fas fa-utensils mr-3 text-lg"></i>
                        Kitchen Management
                        <i class="fas fa-chevron-down ml-auto text-xs transition-transform duration-200"></i>
                    </button>
                    <ul id="kitchen-menu" class="hidden mt-1 space-y-1 pl-6">
                        <li>
                            <a href="/kitchens" class="flex items-center px-3 py-2 text-sm rounded-md text-gray-600 hover:text-gray-900 hover:bg-gray-100">
                                <i class="fas fa-cogs mr-2"></i>
                                Manage Kitchens
                            </a>
                        </li>
                        <li>
                            <a href="/kot-orders" class="flex items-center px-3 py-2 text-sm rounded-md text-gray-600 hover:text-gray-900 hover:bg-gray-100">
                                <i class="fas fa-receipt mr-2"></i>
                                Kitchen Orders (KOT)
                            </a>
                        </li>
                        <li>
                            <a href="/kitchen-display" class="flex items-center px-3 py-2 text-sm rounded-md text-gray-600 hover:text-gray-900 hover:bg-gray-100">
                                <i class="fas fa-tv mr-2"></i>
                                Kitchen Display
                            </a>
                        </li>
                    </ul>
                </div>
            </li>
            
            <!-- Menu Management -->
            <li>
                <div class="relative">
                    <button class="sidebar-dropdown-toggle flex items-center w-full px-3 py-2 text-sm font-medium rounded-md text-gray-700 hover:text-gray-900 hover:bg-gray-100" data-target="menu-management">
                        <i class="fas fa-book-open mr-3 text-lg"></i>
                        Menu Management
                        <i class="fas fa-chevron-down ml-auto text-xs transition-transform duration-200"></i>
                    </button>
                    <ul id="menu-management" class="hidden mt-1 space-y-1 pl-6">
                        <li>
                            <a href="{{ route('menus.index') }}" class="flex items-center px-3 py-2 text-sm rounded-md text-gray-600 hover:text-gray-900 hover:bg-gray-100">
                                <i class="fas fa-list mr-2"></i>
                                Manage Menus
                            </a>
                        </li>
                        <li>
                            <a href="{{ route('categories.index') }}" class="flex items-center px-3 py-2 text-sm rounded-md text-gray-600 hover:text-gray-900 hover:bg-gray-100">
                                <i class="fas fa-tags mr-2"></i>
                                Categories
                            </a>
                        </li>
                        <li>
                            <a href="{{ route('menu-items.index') }}" class="flex items-center px-3 py-2 text-sm rounded-md text-gray-600 hover:text-gray-900 hover:bg-gray-100">
                                <i class="fas fa-hamburger mr-2"></i>
                                Menu Items
                            </a>
                        </li>
                        <li>
                            <a href="{{ route('addons.index') }}" class="flex items-center px-3 py-2 text-sm rounded-md text-gray-600 hover:text-gray-900 hover:bg-gray-100">
                                <i class="fas fa-plus mr-2"></i>
                                Addons
                            </a>
                        </li>
                        <li>
                            <a href="{{ route('variations.index') }}" class="flex items-center px-3 py-2 text-sm rounded-md text-gray-600 hover:text-gray-900 hover:bg-gray-100">
                                <i class="fas fa-layer-group mr-2"></i>
                                Variations
                            </a>
                        </li>
                    </ul>
                </div>
            </li>
            
            <!-- Inventory Management -->
            <li>
                <div class="relative">
                    <button class="sidebar-dropdown-toggle flex items-center w-full px-3 py-2 text-sm font-medium rounded-md text-gray-700 hover:text-gray-900 hover:bg-gray-100" data-target="inventory-menu">
                        <i class="fas fa-boxes mr-3 text-lg"></i>
                        Inventory Management
                        <i class="fas fa-chevron-down ml-auto text-xs transition-transform duration-200"></i>
                    </button>
                    <ul id="inventory-menu" class="hidden mt-1 space-y-1 pl-6">
                        <li>
                            <a href="/inventory/products" class="flex items-center px-3 py-2 text-sm rounded-md text-gray-600 hover:text-gray-900 hover:bg-gray-100">
                                <i class="fas fa-cube mr-2"></i>
                                Products
                            </a>
                        </li>
                        <li>
                            <a href="/inventory/suppliers" class="flex items-center px-3 py-2 text-sm rounded-md text-gray-600 hover:text-gray-900 hover:bg-gray-100">
                                <i class="fas fa-truck mr-2"></i>
                                Suppliers
                            </a>
                        </li>
                        <li>
                            <a href="/inventory/purchase-orders" class="flex items-center px-3 py-2 text-sm rounded-md text-gray-600 hover:text-gray-900 hover:bg-gray-100">
                                <i class="fas fa-file-invoice mr-2"></i>
                                Purchase Orders
                            </a>
                        </li>
                        <li>
                            <a href="/inventory/logs" class="flex items-center px-3 py-2 text-sm rounded-md text-gray-600 hover:text-gray-900 hover:bg-gray-100">
                                <i class="fas fa-history mr-2"></i>
                                Inventory Logs
                            </a>
                        </li>
                    </ul>
                </div>
            </li>
            
            <!-- Customer Management -->
            {{-- <li>
                <div class="relative">
                    <button class="sidebar-dropdown-toggle flex items-center w-full px-3 py-2 text-sm font-medium rounded-md text-gray-700 hover:text-gray-900 hover:bg-gray-100" data-target="customer-menu">
                        <i class="fas fa-users mr-3 text-lg"></i>
                        Customer Management
                        <i class="fas fa-chevron-down ml-auto text-xs transition-transform duration-200"></i>
                    </button>
                    <ul id="customer-menu" class="hidden mt-1 space-y-1 pl-6">
                        <li>
                            <a href="{{ route('customers.index') }}" class="flex items-center px-3 py-2 text-sm rounded-md text-gray-600 hover:text-gray-900 hover:bg-gray-100">
                                <i class="fas fa-list mr-2"></i>
                                Customer List
                            </a>
                        </li>
                      
                        <li>
                            <a href="{{ route('loyalty-programs.index') }}" class="flex items-center px-3 py-2 text-sm rounded-md text-gray-600 hover:text-gray-900 hover:bg-gray-100">
                                <i class="fas fa-gift mr-2"></i>
                                Loyalty Program
                            </a>
                        </li>
                    </ul>
                </div>
            </li> --}}
            
            <!-- Delivery Management -->
            <li>
                <div class="relative">
                    <button class="sidebar-dropdown-toggle flex items-center w-full px-3 py-2 text-sm font-medium rounded-md text-gray-700 hover:text-gray-900 hover:bg-gray-100" data-target="delivery-menu">
                        <i class="fas fa-truck mr-3 text-lg"></i>
                        Delivery Management
                        <i class="fas fa-chevron-down ml-auto text-xs transition-transform duration-200"></i>
                    </button>
                    <ul id="delivery-menu" class="hidden mt-1 space-y-1 pl-6">
                        <li>
                            <a href="{{ route('delivery.personnel.index') }}" class="flex items-center px-3 py-2 text-sm rounded-md text-gray-600 hover:text-gray-900 hover:bg-gray-100">
                                <i class="fas fa-user-tie mr-2"></i>
                                Delivery Personnel
                            </a>
                        </li>
                        <li>
                            <a href="{{ route('delivery.personnel.create') }}" class="flex items-center px-3 py-2 text-sm rounded-md text-gray-600 hover:text-gray-900 hover:bg-gray-100">
                                <i class="fas fa-user-plus mr-2"></i>
                                Add Delivery Staff
                            </a>
                        </li>
                        <li>
                            <a href="/delivery/zones" class="flex items-center px-3 py-2 text-sm rounded-md text-gray-600 hover:text-gray-900 hover:bg-gray-100">
                                <i class="fas fa-map-marked-alt mr-2"></i>
                                Delivery Zones
                            </a>
                        </li>
                        <li>
                            <a href="/delivery/assignments" class="flex items-center px-3 py-2 text-sm rounded-md text-gray-600 hover:text-gray-900 hover:bg-gray-100">
                                <i class="fas fa-clipboard-list mr-2"></i>
                                Assign Deliveries
                            </a>
                        </li>
                        <li>
                            <a href="{{ route('delivery.tracking.index') }}" class="flex items-center px-3 py-2 text-sm rounded-md text-gray-600 hover:text-gray-900 hover:bg-gray-100">
                                <i class="fas fa-route mr-2"></i>
                                Delivery Tracking
                            </a>
                        </li>
                        <li>
                            <a href="{{ route('delivery.reviews.index') }}" class="flex items-center px-3 py-2 text-sm rounded-md text-gray-600 hover:text-gray-900 hover:bg-gray-100">
                                <i class="fas fa-star mr-2"></i>
                                Delivery Reviews
                            </a>
                        </li>
                    </ul>
                </div>
            </li>
            
            <!-- System Management -->
            <li>
                <div class="relative">
                    <button class="sidebar-dropdown-toggle flex items-center w-full px-3 py-2 text-sm font-medium rounded-md text-gray-700 hover:text-gray-900 hover:bg-gray-100" data-target="system-menu">
                        <i class="fas fa-cogs mr-3 text-lg"></i>
                        System Management
                        <i class="fas fa-chevron-down ml-auto text-xs transition-transform duration-200"></i>
                    </button>
                    <ul id="system-menu" class="hidden mt-1 space-y-1 pl-6">
                        <!-- Subscriptions -->
                        <li>
                            <div class="relative">
                                <button class="sidebar-dropdown-toggle flex items-center w-full px-3 py-2 text-sm rounded-md text-gray-600 hover:text-gray-900 hover:bg-gray-100" data-target="subscriptions-submenu">
                                    <i class="fas fa-envelope mr-2"></i>
                                    Subscriptions
                                    <i class="fas fa-chevron-down ml-auto text-xs transition-transform duration-200"></i>
                                </button>
                                <ul id="subscriptions-submenu" class="hidden mt-1 space-y-1 pl-6">
                                    <li>
                                        <a href="{{ route('subscriptions-web.index') }}" class="flex items-center px-3 py-2 text-sm rounded-md text-gray-500 hover:text-gray-700 hover:bg-gray-100">
                                            <i class="fas fa-list mr-2 text-xs"></i>
                                            Subscription List
                                        </a>
                                    </li>
                                    <li>
                                        <a href="{{ route('subscriptions-web.create') }}" class="flex items-center px-3 py-2 text-sm rounded-md text-gray-500 hover:text-gray-700 hover:bg-gray-100">
                                            <i class="fas fa-plus mr-2 text-xs"></i>
                                            Add New Subscription
                                        </a>
                                    </li>
                                </ul>
                            </div>
                        </li>
                        
                        <!-- Packages -->
                        <li>
                            <div class="relative">
                                <button class="sidebar-dropdown-toggle flex items-center w-full px-3 py-2 text-sm rounded-md text-gray-600 hover:text-gray-900 hover:bg-gray-100" data-target="packages-submenu">
                                    <i class="fas fa-box mr-2"></i>
                                    Packages
                                    <i class="fas fa-chevron-down ml-auto text-xs transition-transform duration-200"></i>
                                </button>
                                <ul id="packages-submenu" class="hidden mt-1 space-y-1 pl-6">
                                    <li>
                                        <a href="{{ route('packages.index') }}" class="flex items-center px-3 py-2 text-sm rounded-md text-gray-500 hover:text-gray-700 hover:bg-gray-100">
                                            <i class="fas fa-list mr-2 text-xs"></i>
                                            Package List
                                        </a>
                                    </li>
                                    <li>
                                        <a href="{{ route('packages.create') }}" class="flex items-center px-3 py-2 text-sm rounded-md text-gray-500 hover:text-gray-700 hover:bg-gray-100">
                                            <i class="fas fa-plus mr-2 text-xs"></i>
                                            Add New Package
                                        </a>
                                    </li>
                                </ul>
                            </div>
                        </li>
                        
                        <!-- Tenants -->
                        <li>
                            <div class="relative">
                                <button class="sidebar-dropdown-toggle flex items-center w-full px-3 py-2 text-sm rounded-md text-gray-600 hover:text-gray-900 hover:bg-gray-100" data-target="tenants-submenu">
                                    <i class="fas fa-building mr-2"></i>
                                    Tenants
                                    <i class="fas fa-chevron-down ml-auto text-xs transition-transform duration-200"></i>
                                </button>
                                <ul id="tenants-submenu" class="hidden mt-1 space-y-1 pl-6">
                                    <li>
                                        <a href="{{ route('tenants.index') }}" class="flex items-center px-3 py-2 text-sm rounded-md text-gray-500 hover:text-gray-700 hover:bg-gray-100">
                                            <i class="fas fa-list mr-2 text-xs"></i>
                                            Tenant List
                                        </a>
                                    </li>
                                    <li>
                                        <a href="{{ route('tenants.create') }}" class="flex items-center px-3 py-2 text-sm rounded-md text-gray-500 hover:text-gray-700 hover:bg-gray-100">
                                            <i class="fas fa-plus mr-2 text-xs"></i>
                                            Add New Tenant
                                        </a>
                                    </li>
                                </ul>
                            </div>
                        </li>
                        
                        <!-- Branches -->
                        <li>
                            <div class="relative">
                                <button class="sidebar-dropdown-toggle flex items-center w-full px-3 py-2 text-sm rounded-md text-gray-600 hover:text-gray-900 hover:bg-gray-100" data-target="branches-submenu">
                                    <i class="fas fa-code-branch mr-2"></i>
                                    Branches
                                    <i class="fas fa-chevron-down ml-auto text-xs transition-transform duration-200"></i>
                                </button>
                                <ul id="branches-submenu" class="hidden mt-1 space-y-1 pl-6">
                                    <li>
                                        <a href="{{ route('branches.index') }}" class="flex items-center px-3 py-2 text-sm rounded-md text-gray-500 hover:text-gray-700 hover:bg-gray-100">
                                            <i class="fas fa-list mr-2 text-xs"></i>
                                            Branch List
                                        </a>
                                    </li>
                                    <li>
                                        <a href="{{ route('branches.create') }}" class="flex items-center px-3 py-2 text-sm rounded-md text-gray-500 hover:text-gray-700 hover:bg-gray-100">
                                            <i class="fas fa-plus mr-2 text-xs"></i>
                                            Add New Branch
                                        </a>
                                    </li>
                                </ul>
                            </div>
                        </li>
                        
                        <!-- Users -->
                        <li>
                            <div class="relative">
                                <button class="sidebar-dropdown-toggle flex items-center w-full px-3 py-2 text-sm rounded-md text-gray-600 hover:text-gray-900 hover:bg-gray-100" data-target="users-submenu">
                                    <i class="fas fa-user mr-2"></i>
                                    Users
                                    <i class="fas fa-chevron-down ml-auto text-xs transition-transform duration-200"></i>
                                </button>
                                <ul id="users-submenu" class="hidden mt-1 space-y-1 pl-6">
                                    <li>
                                        <a href="{{ route('users.index') }}" class="flex items-center px-3 py-2 text-sm rounded-md text-gray-500 hover:text-gray-700 hover:bg-gray-100">
                                            <i class="fas fa-list mr-2 text-xs"></i>
                                            User List
                                        </a>
                                    </li>
                                    <li>
                                        <a href="{{ route('users.create') }}" class="flex items-center px-3 py-2 text-sm rounded-md text-gray-500 hover:text-gray-700 hover:bg-gray-100">
                                            <i class="fas fa-user-plus mr-2 text-xs"></i>
                                            Add New User
                                        </a>
                                    </li>
                                </ul>
                            </div>
                        </li>
                        
                        <!-- Roles & Permissions -->
                        <li>
                            <div class="relative">
                                <button class="sidebar-dropdown-toggle flex items-center w-full px-3 py-2 text-sm rounded-md text-gray-600 hover:text-gray-900 hover:bg-gray-100" data-target="roles-submenu">
                                    <i class="fas fa-lock mr-2"></i>
                                    Roles & Permissions
                                    <i class="fas fa-chevron-down ml-auto text-xs transition-transform duration-200"></i>
                                </button>
                                <ul id="roles-submenu" class="hidden mt-1 space-y-1 pl-6">
                                    <li>
                                        <a href="{{ route('roles.index') }}" class="flex items-center px-3 py-2 text-sm rounded-md text-gray-500 hover:text-gray-700 hover:bg-gray-100">
                                            <i class="fas fa-user-tag mr-2 text-xs"></i>
                                            Roles
                                        </a>
                                    </li>
                                    <li>
                                        <a href="{{ route('permissions.index') }}" class="flex items-center px-3 py-2 text-sm rounded-md text-gray-500 hover:text-gray-700 hover:bg-gray-100">
                                            <i class="fas fa-key mr-2 text-xs"></i>
                                            Permissions
                                        </a>
                                    </li>
                                </ul>
                            </div>
                        </li>
                    </ul>
                </div>
            </li>
            
            <!-- Reservations -->
            <li>
                <div class="relative">
                    <button class="sidebar-dropdown-toggle flex items-center w-full px-3 py-2 text-sm font-medium rounded-md text-gray-700 hover:text-gray-900 hover:bg-gray-100" data-target="reservations-menu">
                        <i class="fas fa-chair mr-3 text-lg"></i>
                        Reservations
                        <i class="fas fa-chevron-down ml-auto text-xs transition-transform duration-200"></i>
                    </button>
                    <ul id="reservations-menu" class="hidden mt-1 space-y-1 pl-6">
                        <li>
                            <a href="{{ route('reservation.reservations') }}" class="flex items-center px-3 py-2 text-sm rounded-md text-gray-600 hover:text-gray-900 hover:bg-gray-100">
                                <i class="fas fa-calendar-alt mr-2"></i>
                                All Reservations
                            </a>
                        </li>
                        <li>
                            <a href="{{ route('web.areas.index') }}" class="flex items-center px-3 py-2 text-sm rounded-md text-gray-600 hover:text-gray-900 hover:bg-gray-100">
                                <i class="fas fa-map mr-2"></i>
                                Manage Areas
                            </a>
                        </li>
                        <li>
                            <a href="{{ route('web.tables.index') }}" class="flex items-center px-3 py-2 text-sm rounded-md text-gray-600 hover:text-gray-900 hover:bg-gray-100">
                                <i class="fas fa-table mr-2"></i>
                                Manage Tables
                            </a>
                        </li>
                        <li>
                            <a href="{{ route('reservation.waiter-requests') }}" class="flex items-center px-3 py-2 text-sm rounded-md text-gray-600 hover:text-gray-900 hover:bg-gray-100">
                                <i class="fas fa-bell mr-2"></i>
                                Waiter Requests
                            </a>
                        </li>
                        <li>
                            <a href="{{ route('reservation.qr.test') }}" class="flex items-center px-3 py-2 text-sm rounded-md text-gray-600 hover:text-gray-900 hover:bg-gray-100">
                                <i class="fas fa-qrcode mr-2"></i>
                                QR Code Test
                            </a>
                        </li>
                    </ul>
                </div>
            </li>
            
            <!-- Public Restaurant Page -->
            <li>
                <a href="{{ Auth::check() && Auth::user()->tenant ? route('public.restaurant.show', Auth::user()->tenant->name) : '/menu/restaurant/demo' }}" class="flex items-center px-3 py-2 text-sm font-medium rounded-md text-gray-700 hover:text-gray-900 hover:bg-gray-100">
                    <i class="fas fa-globe mr-3 text-lg"></i>
                    Public Restaurant Page
                </a>
            </li>
            
            <!-- Finance & Payments -->
            <li>
                <div class="relative">
                    <button class="sidebar-dropdown-toggle flex items-center w-full px-3 py-2 text-sm font-medium rounded-md text-gray-700 hover:text-gray-900 hover:bg-gray-100" data-target="finance-menu">
                        <i class="fas fa-credit-card mr-3 text-lg"></i>
                        Finance & Payments
                        <i class="fas fa-chevron-down ml-auto text-xs transition-transform duration-200"></i>
                    </button>
                    <ul id="finance-menu" class="hidden mt-1 space-y-1 pl-6">
                        <li>
                            <a href="{{ route('transactions.index') }}" class="flex items-center px-3 py-2 text-sm rounded-md text-gray-600 hover:text-gray-900 hover:bg-gray-100">
                                <i class="fas fa-exchange-alt mr-2"></i>
                                Transaction Management
                            </a>
                        </li>
                        <li>
                            <a href="{{ route('payments.index') }}" class="flex items-center px-3 py-2 text-sm rounded-md text-gray-600 hover:text-gray-900 hover:bg-gray-100">
                                <i class="fas fa-money-bill mr-2"></i>
                                Payments
                            </a>
                        </li>
                        <li>
                            <a href="/payments/methods" class="flex items-center px-3 py-2 text-sm rounded-md text-gray-600 hover:text-gray-900 hover:bg-gray-100">
                                <i class="fas fa-credit-card mr-2"></i>
                                Payment Methods
                            </a>
                        </li>
                        <li>
                            <a href="/payments/refunds" class="flex items-center px-3 py-2 text-sm rounded-md text-gray-600 hover:text-gray-900 hover:bg-gray-100">
                                <i class="fas fa-undo mr-2"></i>
                                Refunds
                            </a>
                        </li>
                    </ul>
                </div>
            </li>
            
            <!-- HR (Human Resources) -->
            <li>
                <div class="relative">
                    <button class="sidebar-dropdown-toggle flex items-center w-full px-3 py-2 text-sm font-medium rounded-md text-gray-700 hover:text-gray-900 hover:bg-gray-100" data-target="hr-menu">
                        <i class="fas fa-user-tie mr-3 text-lg"></i>
                        HR (Human Resources)
                        <i class="fas fa-chevron-down ml-auto text-xs transition-transform duration-200"></i>
                    </button>
                    <ul id="hr-menu" class="hidden mt-1 space-y-1 pl-6">
                        <li>
                            <a href="/hr/staff" class="flex items-center px-3 py-2 text-sm rounded-md text-gray-600 hover:text-gray-900 hover:bg-gray-100">
                                <i class="fas fa-users mr-2"></i>
                                Manage Staff
                            </a>
                        </li>
                        <li>
                            <a href="/hr/attendance" class="flex items-center px-3 py-2 text-sm rounded-md text-gray-600 hover:text-gray-900 hover:bg-gray-100">
                                <i class="fas fa-clock mr-2"></i>
                                Attendance
                            </a>
                        </li>
                        <li>
                            <a href="/hr/shifts" class="flex items-center px-3 py-2 text-sm rounded-md text-gray-600 hover:text-gray-900 hover:bg-gray-100">
                                <i class="fas fa-calendar-week mr-2"></i>
                                Shifts
                            </a>
                        </li>
                        <li>
                            <a href="/hr/leave-requests" class="flex items-center px-3 py-2 text-sm rounded-md text-gray-600 hover:text-gray-900 hover:bg-gray-100">
                                <i class="fas fa-calendar-times mr-2"></i>
                                Leave Requests
                            </a>
                        </li>
                        <li>
                            <a href="/hr/penalties" class="flex items-center px-3 py-2 text-sm rounded-md text-gray-600 hover:text-gray-900 hover:bg-gray-100">
                                <i class="fas fa-exclamation-triangle mr-2"></i>
                                Penalties
                            </a>
                        </li>
                    </ul>
                </div>
            </li>
            
            <!-- Payroll -->
            <li>
                <div class="relative">
                    <button class="sidebar-dropdown-toggle flex items-center w-full px-3 py-2 text-sm font-medium rounded-md text-gray-700 hover:text-gray-900 hover:bg-gray-100" data-target="payroll-menu">
                        <i class="fas fa-money-check-alt mr-3 text-lg"></i>
                        Payroll
                        <i class="fas fa-chevron-down ml-auto text-xs transition-transform duration-200"></i>
                    </button>
                    <ul id="payroll-menu" class="hidden mt-1 space-y-1 pl-6">
                        <li>
                            <a href="/payroll/payslips" class="flex items-center px-3 py-2 text-sm rounded-md text-gray-600 hover:text-gray-900 hover:bg-gray-100">
                                <i class="fas fa-file-invoice-dollar mr-2"></i>
                                Payslips
                            </a>
                        </li>
                        <li>
                            <a href="/payroll/periods" class="flex items-center px-3 py-2 text-sm rounded-md text-gray-600 hover:text-gray-900 hover:bg-gray-100">
                                <i class="fas fa-calendar mr-2"></i>
                                Pay Periods
                            </a>
                        </li>
                    </ul>
                </div>
            </li>
            
            <!-- Stock Control -->
            <li>
                <div class="relative">
                    <button class="sidebar-dropdown-toggle flex items-center w-full px-3 py-2 text-sm font-medium rounded-md text-gray-700 hover:text-gray-900 hover:bg-gray-100" data-target="stock-menu">
                        <i class="fas fa-warehouse mr-3 text-lg"></i>
                        Stock Control
                        <i class="fas fa-chevron-down ml-auto text-xs transition-transform duration-200"></i>
                    </button>
                    <ul id="stock-menu" class="hidden mt-1 space-y-1 pl-6">
                        <li>
                            <a href="{{ route('inventory.index') }}" class="flex items-center px-3 py-2 text-sm rounded-md text-gray-600 hover:text-gray-900 hover:bg-gray-100">
                                <i class="fas fa-tachometer-alt mr-2"></i>
                                Inventory Dashboard
                            </a>
                        </li>
                        <li>
                            <a href="{{ route('inventory.units.index') }}" class="flex items-center px-3 py-2 text-sm rounded-md text-gray-600 hover:text-gray-900 hover:bg-gray-100">
                                <i class="fas fa-ruler mr-2"></i>
                                Units
                            </a>
                        </li>
                        <li>
                            <a href="{{ route('inventory.categories.index') }}" class="flex items-center px-3 py-2 text-sm rounded-md text-gray-600 hover:text-gray-900 hover:bg-gray-100">
                                <i class="fas fa-tags mr-2"></i>
                                Material Categories
                            </a>
                        </li>
                        <li>
                            <a href="{{ route('inventory.items.index') }}" class="flex items-center px-3 py-2 text-sm rounded-md text-gray-600 hover:text-gray-900 hover:bg-gray-100">
                                <i class="fas fa-boxes mr-2"></i>
                                Manage Materials
                            </a>
                        </li>
                        <li>
                            <a href="{{ route('inventory.stock.index') }}" class="flex items-center px-3 py-2 text-sm rounded-md text-gray-600 hover:text-gray-900 hover:bg-gray-100">
                                <i class="fas fa-cubes mr-2"></i>
                                Stock Management
                            </a>
                        </li>
                        <li>
                            <a href="{{ route('inventory.stock.movements') }}" class="flex items-center px-3 py-2 text-sm rounded-md text-gray-600 hover:text-gray-900 hover:bg-gray-100">
                                <i class="fas fa-arrows-alt mr-2"></i>
                                Stock Movements
                            </a>
                        </li>
                        <li>
                            <a href="{{ route('inventory.recipes.index') }}" class="flex items-center px-3 py-2 text-sm rounded-md text-gray-600 hover:text-gray-900 hover:bg-gray-100">
                                <i class="fas fa-book mr-2"></i>
                                Recipes
                            </a>
                        </li>
                        <li>
                            <a href="{{ route('inventory.purchase-orders.index') }}" class="flex items-center px-3 py-2 text-sm rounded-md text-gray-600 hover:text-gray-900 hover:bg-gray-100">
                                <i class="fas fa-shopping-cart mr-2"></i>
                                Purchase Orders
                            </a>
                        </li>
                        <li>
                            <a href="{{ route('inventory.suppliers.index') }}" class="flex items-center px-3 py-2 text-sm rounded-md text-gray-600 hover:text-gray-900 hover:bg-gray-100">
                                <i class="fas fa-truck mr-2"></i>
                                Suppliers
                            </a>
                        </li>
                    </ul>
                </div>
            </li>

            <!-- Reports -->
            <li>
                <div class="relative">
                    <button class="sidebar-dropdown-toggle flex items-center w-full px-3 py-2 text-sm font-medium rounded-md text-gray-700 hover:text-gray-900 hover:bg-gray-100" data-target="reports-menu">
                        <i class="fas fa-chart-bar mr-3 text-lg"></i>
                        Reports
                        <i class="fas fa-chevron-down ml-auto text-xs transition-transform duration-200"></i>
                    </button>
                    <ul id="reports-menu" class="hidden mt-1 space-y-1 pl-6">
                        <!-- Sales Reports -->
                        <li>
                            <div class="relative">
                                <button class="sidebar-dropdown-toggle flex items-center w-full px-3 py-2 text-sm rounded-md text-gray-600 hover:text-gray-900 hover:bg-gray-100" data-target="sales-reports-submenu">
                                    <i class="fas fa-chart-line mr-2"></i>
                                    Sales Reports
                                    <i class="fas fa-chevron-down ml-auto text-xs transition-transform duration-200"></i>
                                </button>
                                <ul id="sales-reports-submenu" class="hidden mt-1 space-y-1 pl-6">
                                    <li>
                                        <a href="{{ route('reports.sales.daily') }}" class="flex items-center px-3 py-2 text-sm rounded-md text-gray-500 hover:text-gray-700 hover:bg-gray-100">
                                            <i class="fas fa-calendar-day mr-2 text-xs"></i>
                                            Daily Sales
                                        </a>
                                    </li>
                                    <li>
                                        <a href="{{ route('reports.sales.monthly') }}" class="flex items-center px-3 py-2 text-sm rounded-md text-gray-500 hover:text-gray-700 hover:bg-gray-100">
                                            <i class="fas fa-calendar-alt mr-2 text-xs"></i>
                                            Monthly Sales
                                        </a>
                                    </li>
                                    <li>
                                        <a href="{{ route('reports.sales.yearly') }}" class="flex items-center px-3 py-2 text-sm rounded-md text-gray-500 hover:text-gray-700 hover:bg-gray-100">
                                            <i class="fas fa-calendar mr-2 text-xs"></i>
                                            Yearly Sales
                                        </a>
                                    </li>
                                    <li>
                                        <a href="{{ route('reports.sales.items') }}" class="flex items-center px-3 py-2 text-sm rounded-md text-gray-500 hover:text-gray-700 hover:bg-gray-100">
                                            <i class="fas fa-hamburger mr-2 text-xs"></i>
                                            Item Sales
                                        </a>
                                    </li>
                                </ul>
                            </div>
                        </li>

                        <!-- Financial Reports -->
                        <li>
                            <div class="relative">
                                <button class="sidebar-dropdown-toggle flex items-center w-full px-3 py-2 text-sm rounded-md text-gray-600 hover:text-gray-900 hover:bg-gray-100" data-target="financial-reports-submenu">
                                    <i class="fas fa-dollar-sign mr-2"></i>
                                    Financial Reports
                                    <i class="fas fa-chevron-down ml-auto text-xs transition-transform duration-200"></i>
                                </button>
                                <ul id="financial-reports-submenu" class="hidden mt-1 space-y-1 pl-6">
                                    <li>
                                        <a href="{{ route('reports.financial.profit-loss') }}" class="flex items-center px-3 py-2 text-sm rounded-md text-gray-500 hover:text-gray-700 hover:bg-gray-100">
                                            <i class="fas fa-chart-pie mr-2 text-xs"></i>
                                            Profit & Loss
                                        </a>
                                    </li>
                                    <li>
                                        <a href="{{ route('reports.financial.revenue') }}" class="flex items-center px-3 py-2 text-sm rounded-md text-gray-500 hover:text-gray-700 hover:bg-gray-100">
                                            <i class="fas fa-money-bill-wave mr-2 text-xs"></i>
                                            Revenue Report
                                        </a>
                                    </li>
                                    <li>
                                        <a href="{{ route('reports.financial.expenses') }}" class="flex items-center px-3 py-2 text-sm rounded-md text-gray-500 hover:text-gray-700 hover:bg-gray-100">
                                            <i class="fas fa-receipt mr-2 text-xs"></i>
                                            Expenses Report
                                        </a>
                                    </li>
                                    <li>
                                        <a href="{{ route('reports.financial.tax') }}" class="flex items-center px-3 py-2 text-sm rounded-md text-gray-500 hover:text-gray-700 hover:bg-gray-100">
                                            <i class="fas fa-percentage mr-2 text-xs"></i>
                                            Tax Report
                                        </a>
                                    </li>
                                </ul>
                            </div>
                        </li>

                        <!-- Inventory Reports -->
                        <li>
                            <div class="relative">
                                <button class="sidebar-dropdown-toggle flex items-center w-full px-3 py-2 text-sm rounded-md text-gray-600 hover:text-gray-900 hover:bg-gray-100" data-target="inventory-reports-submenu">
                                    <i class="fas fa-boxes mr-2"></i>
                                    Inventory Reports
                                    <i class="fas fa-chevron-down ml-auto text-xs transition-transform duration-200"></i>
                                </button>
                                <ul id="inventory-reports-submenu" class="hidden mt-1 space-y-1 pl-6">
                                    <li>
                                        <a href="{{ route('reports.inventory.stock-levels') }}" class="flex items-center px-3 py-2 text-sm rounded-md text-gray-500 hover:text-gray-700 hover:bg-gray-100">
                                            <i class="fas fa-layer-group mr-2 text-xs"></i>
                                            Stock Levels
                                        </a>
                                    </li>
                                    <li>
                                        <a href="{{ route('reports.inventory.low-stock') }}" class="flex items-center px-3 py-2 text-sm rounded-md text-gray-500 hover:text-gray-700 hover:bg-gray-100">
                                            <i class="fas fa-exclamation-triangle mr-2 text-xs"></i>
                                            Low Stock Alert
                                        </a>
                                    </li>
                                    <li>
                                        <a href="{{ route('reports.inventory.movements') }}" class="flex items-center px-3 py-2 text-sm rounded-md text-gray-500 hover:text-gray-700 hover:bg-gray-100">
                                            <i class="fas fa-arrows-alt mr-2 text-xs"></i>
                                            Stock Movements
                                        </a>
                                    </li>
                                    <li>
                                        <a href="{{ route('reports.inventory.waste') }}" class="flex items-center px-3 py-2 text-sm rounded-md text-gray-500 hover:text-gray-700 hover:bg-gray-100">
                                            <i class="fas fa-trash mr-2 text-xs"></i>
                                            Waste Report
                                        </a>
                                    </li>
                                </ul>
                            </div>
                        </li>

                        <!-- Staff Reports -->
                        <li>
                            <div class="relative">
                                <button class="sidebar-dropdown-toggle flex items-center w-full px-3 py-2 text-sm rounded-md text-gray-600 hover:text-gray-900 hover:bg-gray-100" data-target="staff-reports-submenu">
                                    <i class="fas fa-users mr-2"></i>
                                    Staff Reports
                                    <i class="fas fa-chevron-down ml-auto text-xs transition-transform duration-200"></i>
                                </button>
                                <ul id="staff-reports-submenu" class="hidden mt-1 space-y-1 pl-6">
                                    <li>
                                        <a href="{{ route('reports.staff.attendance') }}" class="flex items-center px-3 py-2 text-sm rounded-md text-gray-500 hover:text-gray-700 hover:bg-gray-100">
                                            <i class="fas fa-clock mr-2 text-xs"></i>
                                            Attendance Report
                                        </a>
                                    </li>
                                    <li>
                                        <a href="{{ route('reports.staff.performance') }}" class="flex items-center px-3 py-2 text-sm rounded-md text-gray-500 hover:text-gray-700 hover:bg-gray-100">
                                            <i class="fas fa-chart-area mr-2 text-xs"></i>
                                            Performance Report
                                        </a>
                                    </li>
                                    <li>
                                        <a href="{{ route('reports.staff.payroll') }}" class="flex items-center px-3 py-2 text-sm rounded-md text-gray-500 hover:text-gray-700 hover:bg-gray-100">
                                            <i class="fas fa-money-check-alt mr-2 text-xs"></i>
                                            Payroll Report
                                        </a>
                                    </li>
                                    <li>
                                        <a href="{{ route('reports.staff.working-hours') }}" class="flex items-center px-3 py-2 text-sm rounded-md text-gray-500 hover:text-gray-700 hover:bg-gray-100">
                                            <i class="fas fa-business-time mr-2 text-xs"></i>
                                            Working Hours
                                        </a>
                                    </li>
                                </ul>
                            </div>
                        </li>

                        <!-- Customer Reports -->
                        <li>
                            <div class="relative">
                                <button class="sidebar-dropdown-toggle flex items-center w-full px-3 py-2 text-sm rounded-md text-gray-600 hover:text-gray-900 hover:bg-gray-100" data-target="customer-reports-submenu">
                                    <i class="fas fa-user-friends mr-2"></i>
                                    Customer Reports
                                    <i class="fas fa-chevron-down ml-auto text-xs transition-transform duration-200"></i>
                                </button>
                                <ul id="customer-reports-submenu" class="hidden mt-1 space-y-1 pl-6">
                                    <li>
                                        <a href="{{ route('reports.customers.analytics') }}" class="flex items-center px-3 py-2 text-sm rounded-md text-gray-500 hover:text-gray-700 hover:bg-gray-100">
                                            <i class="fas fa-analytics mr-2 text-xs"></i>
                                            Customer Analytics
                                        </a>
                                    </li>
                                    <li>
                                        <a href="{{ route('reports.customers.loyalty') }}" class="flex items-center px-3 py-2 text-sm rounded-md text-gray-500 hover:text-gray-700 hover:bg-gray-100">
                                            <i class="fas fa-heart mr-2 text-xs"></i>
                                            Loyalty Report
                                        </a>
                                    </li>
                                    <li>
                                        <a href="{{ route('reports.customers.feedback') }}" class="flex items-center px-3 py-2 text-sm rounded-md text-gray-500 hover:text-gray-700 hover:bg-gray-100">
                                            <i class="fas fa-comments mr-2 text-xs"></i>
                                            Customer Feedback
                                        </a>
                                    </li>
                                </ul>
                            </div>
                        </li>
                    </ul>
                </div>
            </li>

            <!-- Settings -->
            <li>
                <div class="relative">
                    <button class="sidebar-dropdown-toggle flex items-center w-full px-3 py-2 text-sm font-medium rounded-md text-gray-700 hover:text-gray-900 hover:bg-gray-100" data-target="settings-menu">
                        <i class="fas fa-cog mr-3 text-lg"></i>
                        Settings
                        <i class="fas fa-chevron-down ml-auto text-xs transition-transform duration-200"></i>
                    </button>
                    <ul id="settings-menu" class="hidden mt-1 space-y-1 pl-6">
                        <!-- General Settings -->
                        <li>
                            <div class="relative">
                                <button class="sidebar-dropdown-toggle flex items-center w-full px-3 py-2 text-sm rounded-md text-gray-600 hover:text-gray-900 hover:bg-gray-100" data-target="general-settings-submenu">
                                    <i class="fas fa-sliders-h mr-2"></i>
                                    General Settings
                                    <i class="fas fa-chevron-down ml-auto text-xs transition-transform duration-200"></i>
                                </button>
                                <ul id="general-settings-submenu" class="hidden mt-1 space-y-1 pl-6">
                                    <li>
                                        <a href="{{ route('settings.general.restaurant') }}" class="flex items-center px-3 py-2 text-sm rounded-md text-gray-500 hover:text-gray-700 hover:bg-gray-100">
                                            <i class="fas fa-store mr-2 text-xs"></i>
                                            Restaurant Info
                                        </a>
                                    </li>
                                    <li>
                                        <a href="{{ route('settings.general.business') }}" class="flex items-center px-3 py-2 text-sm rounded-md text-gray-500 hover:text-gray-700 hover:bg-gray-100">
                                            <i class="fas fa-briefcase mr-2 text-xs"></i>
                                            Business Settings
                                        </a>
                                    </li>
                                    <li>
                                        <a href="{{ route('settings.general.localization') }}" class="flex items-center px-3 py-2 text-sm rounded-md text-gray-500 hover:text-gray-700 hover:bg-gray-100">
                                            <i class="fas fa-globe mr-2 text-xs"></i>
                                            Localization
                                        </a>
                                    </li>
                                    <li>
                                        <a href="{{ route('settings.general.timezone') }}" class="flex items-center px-3 py-2 text-sm rounded-md text-gray-500 hover:text-gray-700 hover:bg-gray-100">
                                            <i class="fas fa-clock mr-2 text-xs"></i>
                                            Timezone
                                        </a>
                                    </li>
                                </ul>
                            </div>
                        </li>

                        <!-- POS Settings -->
                        <li>
                            <div class="relative">
                                <button class="sidebar-dropdown-toggle flex items-center w-full px-3 py-2 text-sm rounded-md text-gray-600 hover:text-gray-900 hover:bg-gray-100" data-target="pos-settings-submenu">
                                    <i class="fas fa-cash-register mr-2"></i>
                                    POS Settings
                                    <i class="fas fa-chevron-down ml-auto text-xs transition-transform duration-200"></i>
                                </button>
                                <ul id="pos-settings-submenu" class="hidden mt-1 space-y-1 pl-6">
                                    <li>
                                        <a href="{{ route('settings.pos.receipt') }}" class="flex items-center px-3 py-2 text-sm rounded-md text-gray-500 hover:text-gray-700 hover:bg-gray-100">
                                            <i class="fas fa-receipt mr-2 text-xs"></i>
                                            Receipt Settings
                                        </a>
                                    </li>
                                    <li>
                                        <a href="{{ route('settings.pos.printer') }}" class="flex items-center px-3 py-2 text-sm rounded-md text-gray-500 hover:text-gray-700 hover:bg-gray-100">
                                            <i class="fas fa-print mr-2 text-xs"></i>
                                            Printer Settings
                                        </a>
                                    </li>
                                    <li>
                                        <a href="{{ route('settings.pos.display') }}" class="flex items-center px-3 py-2 text-sm rounded-md text-gray-500 hover:text-gray-700 hover:bg-gray-100">
                                            <i class="fas fa-desktop mr-2 text-xs"></i>
                                            Display Settings
                                        </a>
                                    </li>
                                    <li>
                                        <a href="{{ route('settings.pos.shortcuts') }}" class="flex items-center px-3 py-2 text-sm rounded-md text-gray-500 hover:text-gray-700 hover:bg-gray-100">
                                            <i class="fas fa-keyboard mr-2 text-xs"></i>
                                            Keyboard Shortcuts
                                        </a>
                                    </li>
                                </ul>
                            </div>
                        </li>

                        <!-- Payment Settings -->
                        <li>
                            <div class="relative">
                                <button class="sidebar-dropdown-toggle flex items-center w-full px-3 py-2 text-sm rounded-md text-gray-600 hover:text-gray-900 hover:bg-gray-100" data-target="payment-settings-submenu">
                                    <i class="fas fa-credit-card mr-2"></i>
                                    Payment Settings
                                    <i class="fas fa-chevron-down ml-auto text-xs transition-transform duration-200"></i>
                                </button>
                                <ul id="payment-settings-submenu" class="hidden mt-1 space-y-1 pl-6">
                                    <li>
                                        <a href="{{ route('settings.payment.methods') }}" class="flex items-center px-3 py-2 text-sm rounded-md text-gray-500 hover:text-gray-700 hover:bg-gray-100">
                                            <i class="fas fa-money-bill mr-2 text-xs"></i>
                                            Payment Methods
                                        </a>
                                    </li>
                                    <li>
                                        <a href="{{ route('settings.payment.gateways') }}" class="flex items-center px-3 py-2 text-sm rounded-md text-gray-500 hover:text-gray-700 hover:bg-gray-100">
                                            <i class="fas fa-credit-card mr-2 text-xs"></i>
                                            Payment Gateways
                                        </a>
                                    </li>
                                    <li>
                                        <a href="{{ route('settings.payment.tax') }}" class="flex items-center px-3 py-2 text-sm rounded-md text-gray-500 hover:text-gray-700 hover:bg-gray-100">
                                            <i class="fas fa-percentage mr-2 text-xs"></i>
                                            Tax Configuration
                                        </a>
                                    </li>
                                    <li>
                                        <a href="{{ route('settings.payment.discounts') }}" class="flex items-center px-3 py-2 text-sm rounded-md text-gray-500 hover:text-gray-700 hover:bg-gray-100">
                                            <i class="fas fa-tags mr-2 text-xs"></i>
                                            Discounts & Coupons
                                        </a>
                                    </li>
                                </ul>
                            </div>
                        </li>

                        <!-- Notification Settings -->
                        <li>
                            <div class="relative">
                                <button class="sidebar-dropdown-toggle flex items-center w-full px-3 py-2 text-sm rounded-md text-gray-600 hover:text-gray-900 hover:bg-gray-100" data-target="notification-settings-submenu">
                                    <i class="fas fa-bell mr-2"></i>
                                    Notifications
                                    <i class="fas fa-chevron-down ml-auto text-xs transition-transform duration-200"></i>
                                </button>
                                <ul id="notification-settings-submenu" class="hidden mt-1 space-y-1 pl-6">
                                    <li>
                                        <a href="{{ route('settings.notifications.email') }}" class="flex items-center px-3 py-2 text-sm rounded-md text-gray-500 hover:text-gray-700 hover:bg-gray-100">
                                            <i class="fas fa-envelope mr-2 text-xs"></i>
                                            Email Notifications
                                        </a>
                                    </li>
                                    <li>
                                        <a href="{{ route('settings.notifications.sms') }}" class="flex items-center px-3 py-2 text-sm rounded-md text-gray-500 hover:text-gray-700 hover:bg-gray-100">
                                            <i class="fas fa-sms mr-2 text-xs"></i>
                                            SMS Notifications
                                        </a>
                                    </li>
                                    <li>
                                        <a href="{{ route('settings.notifications.push') }}" class="flex items-center px-3 py-2 text-sm rounded-md text-gray-500 hover:text-gray-700 hover:bg-gray-100">
                                            <i class="fas fa-mobile-alt mr-2 text-xs"></i>
                                            Push Notifications
                                        </a>
                                    </li>
                                    <li>
                                        <a href="{{ route('settings.notifications.alerts') }}" class="flex items-center px-3 py-2 text-sm rounded-md text-gray-500 hover:text-gray-700 hover:bg-gray-100">
                                            <i class="fas fa-exclamation-circle mr-2 text-xs"></i>
                                            System Alerts
                                        </a>
                                    </li>
                                </ul>
                            </div>
                        </li>

                        <!-- Security Settings -->
                        <li>
                            <div class="relative">
                                <button class="sidebar-dropdown-toggle flex items-center w-full px-3 py-2 text-sm rounded-md text-gray-600 hover:text-gray-900 hover:bg-gray-100" data-target="security-settings-submenu">
                                    <i class="fas fa-shield-alt mr-2"></i>
                                    Security
                                    <i class="fas fa-chevron-down ml-auto text-xs transition-transform duration-200"></i>
                                </button>
                                <ul id="security-settings-submenu" class="hidden mt-1 space-y-1 pl-6">
                                    <li>
                                        <a href="{{ route('settings.security.password-policy') }}" class="flex items-center px-3 py-2 text-sm rounded-md text-gray-500 hover:text-gray-700 hover:bg-gray-100">
                                            <i class="fas fa-key mr-2 text-xs"></i>
                                            Password Policy
                                        </a>
                                    </li>
                                    <li>
                                        <a href="{{ route('settings.security.two-factor') }}" class="flex items-center px-3 py-2 text-sm rounded-md text-gray-500 hover:text-gray-700 hover:bg-gray-100">
                                            <i class="fas fa-mobile-alt mr-2 text-xs"></i>
                                            Two-Factor Auth
                                        </a>
                                    </li>
                                    <li>
                                        <a href="{{ route('settings.security.session') }}" class="flex items-center px-3 py-2 text-sm rounded-md text-gray-500 hover:text-gray-700 hover:bg-gray-100">
                                            <i class="fas fa-clock mr-2 text-xs"></i>
                                            Session Management
                                        </a>
                                    </li>
                                    <li>
                                        <a href="{{ route('settings.security.audit-log') }}" class="flex items-center px-3 py-2 text-sm rounded-md text-gray-500 hover:text-gray-700 hover:bg-gray-100">
                                            <i class="fas fa-history mr-2 text-xs"></i>
                                            Audit Log
                                        </a>
                                    </li>
                                </ul>
                            </div>
                        </li>

                        <!-- System Settings -->
                        <li>
                            <div class="relative">
                                <button class="sidebar-dropdown-toggle flex items-center w-full px-3 py-2 text-sm rounded-md text-gray-600 hover:text-gray-900 hover:bg-gray-100" data-target="system-settings-submenu">
                                    <i class="fas fa-server mr-2"></i>
                                    System Settings
                                    <i class="fas fa-chevron-down ml-auto text-xs transition-transform duration-200"></i>
                                </button>
                                <ul id="system-settings-submenu" class="hidden mt-1 space-y-1 pl-6">
                                    <li>
                                        <a href="{{ route('settings.system.backup') }}" class="flex items-center px-3 py-2 text-sm rounded-md text-gray-500 hover:text-gray-700 hover:bg-gray-100">
                                            <i class="fas fa-database mr-2 text-xs"></i>
                                            Backup & Restore
                                        </a>
                                    </li>
                                    <li>
                                        <a href="{{ route('settings.system.maintenance') }}" class="flex items-center px-3 py-2 text-sm rounded-md text-gray-500 hover:text-gray-700 hover:bg-gray-100">
                                            <i class="fas fa-tools mr-2 text-xs"></i>
                                            Maintenance Mode
                                        </a>
                                    </li>
                                    <li>
                                        <a href="{{ route('settings.system.logs') }}" class="flex items-center px-3 py-2 text-sm rounded-md text-gray-500 hover:text-gray-700 hover:bg-gray-100">
                                            <i class="fas fa-file-alt mr-2 text-xs"></i>
                                            System Logs
                                        </a>
                                    </li>
                                    <li>
                                        <a href="{{ route('settings.system.cache') }}" class="flex items-center px-3 py-2 text-sm rounded-md text-gray-500 hover:text-gray-700 hover:bg-gray-100">
                                            <i class="fas fa-memory mr-2 text-xs"></i>
                                            Cache Management
                                        </a>
                                    </li>
                                </ul>
                            </div>
                        </li>
                    </ul>
                </div>
            </li>
        </ul>
        
        <!-- Bottom section -->
        <div class="mt-8 pt-4 border-t border-gray-200">
            <div class="px-3 py-2">
                <div class="flex items-center space-x-3">
                    <div class="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
                        <i class="fas fa-wifi text-green-600 text-sm"></i>
                    </div>
                    <div>
                        <p class="text-xs font-medium text-gray-900">System Status</p>
                        <p class="text-xs text-green-600">Online</p>
                    </div>
                </div>
            </div>
        </div>
    </nav>
</aside>

<style>
/* Hide scrollbar for sidebar navigation */
.sidebar-scrollbar-hidden {
    /* Hide scrollbar for Chrome, Safari and Opera */
    -webkit-scrollbar: none;
    /* Hide scrollbar for IE, Edge and Firefox */
    -ms-overflow-style: none;
    scrollbar-width: none;
}

/* Ensure scrolling still works */
.sidebar-scrollbar-hidden::-webkit-scrollbar {
    display: none;
}
</style>

<script>
$(document).ready(function() {
    // Sidebar dropdown toggle
    $('.sidebar-dropdown-toggle').click(function() {
        const target = $(this).data('target');
        const targetMenu = $('#' + target);
        const chevron = $(this).find('.fa-chevron-down');

        // Close other open menus at the same level
        $(this).closest('ul').find('.sidebar-dropdown-toggle').not(this).each(function() {
            const otherTarget = $(this).data('target');
            const otherMenu = $('#' + otherTarget);
            const otherChevron = $(this).find('.fa-chevron-down');

            if (otherMenu.is(':visible')) {
                otherMenu.slideUp(200);
                otherChevron.removeClass('rotate-180');
            }
        });

        // Toggle current menu
        targetMenu.slideToggle(200);
        chevron.toggleClass('rotate-180');
    });

    // Mobile sidebar close button
    $('#sidebar-close').click(function() {
        $('#sidebar').removeClass('translate-x-0').addClass('-translate-x-full');
        $('#mobile-overlay').addClass('hidden');
    });

    // Branch dropdown toggle
    $('#branchDropdownButton').click(function(e) {
        e.preventDefault();
        e.stopPropagation();
        $('#branchDropdownMenu').toggleClass('hidden');
    });

    // Close branch dropdown when clicking outside
    $(document).click(function(e) {
        if (!$(e.target).closest('#branchDropdownButton, #branchDropdownMenu').length) {
            $('#branchDropdownMenu').addClass('hidden');
        }
    });

    // Prevent dropdown from closing when clicking inside the menu
    $('#branchDropdownMenu').click(function(e) {
        e.stopPropagation();
    });
});
</script>