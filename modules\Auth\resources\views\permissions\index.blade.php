@extends('layouts.master')
@push('styles')
<meta name="csrf-token" content="{{ csrf_token() }}">
<!-- DataTables CSS -->
<link rel="stylesheet" href="https://cdn.datatables.net/1.13.7/css/jquery.dataTables.min.css">
<link rel="stylesheet" href="https://cdn.datatables.net/buttons/2.4.2/css/buttons.dataTables.min.css">
<link rel="stylesheet" href="https://cdn.datatables.net/responsive/2.5.0/css/responsive.dataTables.min.css">
<!-- Select2 CSS -->
<link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet" />
<style>
    /* Custom DataTables Tailwind Styling */
    .dataTables_wrapper {
        font-family: inherit;
    }
    
    .dataTables_wrapper .dataTables_length,
    .dataTables_wrapper .dataTables_filter,
    .dataTables_wrapper .dataTables_info,
    .dataTables_wrapper .dataTables_processing,
    .dataTables_wrapper .dataTables_paginate {
        color: #374151;
        font-size: 0.875rem;
        padding: 0.5rem 0.75rem;
    }
    .dataTables_processing{
        margin: auto
    }
    .dataTables_wrapper .dataTables_length select,
    .dataTables_wrapper .dataTables_filter input {
        border: 1px solid #d1d5db;
        border-radius: 0.375rem;
        padding: 0.375rem 0.75rem;
        font-size: 0.875rem;
    }
    
    .dataTables_wrapper .dataTables_filter input:focus {
        outline: none;
        border-color: #3b82f6;
        box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
    }
    
    .dataTables_wrapper .dataTables_paginate .paginate_button {
        padding: 0.5rem 0.75rem !important;
        margin: 0 0.125rem !important;
        border-radius: 0.375rem !important;
        border: 1px solid #d1d5db !important;
        background: white !important;
        color: #374151 !important;
        text-decoration: none !important;
    }
    
    .dataTables_wrapper .dataTables_paginate .paginate_button:hover {
        background: #f3f4f6 !important;
        border-color: #9ca3af !important;
        color: #374151 !important;
    }
    
    .dataTables_wrapper .dataTables_paginate .paginate_button.current {
        background: #3b82f6 !important;
        border-color: #3b82f6 !important;
        color: white !important;
    }
    
    .dataTables_wrapper .dataTables_paginate .paginate_button.disabled {
        background: #f9fafb !important;
        color: #9ca3af !important;
        border-color: #e5e7eb !important;
    }
    
    table.dataTable {
        border-collapse: separate !important;
        border-spacing: 0 !important;
    }
    
    table.dataTable thead th {
        background-color: #f9fafb;
        border-bottom: 1px solid #e5e7eb;
        font-weight: 600;
        color: #374151;
        padding: 0.75rem;
    }
    
    table.dataTable tbody td {
        padding: 0.75rem;
        border-bottom: 1px solid #f3f4f6;
    }
    
    table.dataTable tbody tr:hover {
        background-color: #f9fafb;
    }
    
    .select2-container--default .select2-selection--single {
        height: 42px;
        border: 1px solid #d1d5db;
        border-radius: 0.5rem;
        padding: 0.5rem;
    }
    
    .select2-container--default .select2-selection--single .select2-selection__rendered {
        line-height: 26px;
        padding-left: 0;
    }
    
    .select2-container--default .select2-selection--single .select2-selection__arrow {
        height: 40px;
    }
    
    .modal-backdrop {
        background-color: rgba(0, 0, 0, 0.5);
    }
    
    .animate-fadeIn {
        animation: fadeIn 0.3s ease-in-out;
    }
    
    @keyframes fadeIn {
        from { opacity: 0; transform: scale(0.95); }
        to { opacity: 1; transform: scale(1); }
    }
    
    .animate-slideDown {
        animation: slideDown 0.3s ease-in-out;
    }
    
    @keyframes slideDown {
        from { opacity: 0; transform: translateY(-10px); }
        to { opacity: 1; transform: translateY(0); }
    }
</style>
@endpush
@section('content')
<!-- Page Header -->
<div class="mb-6">
    <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between">
        <div class="mb-4 sm:mb-0">
            <nav class="flex mb-2" aria-label="Breadcrumb">
                <ol class="inline-flex items-center space-x-1 md:space-x-3">
                    <li class="inline-flex items-center">
                        <span class="text-gray-700 text-sm font-medium">إدارة النظام</span>
                    </li>
                    <li>
                        <div class="flex items-center">
                            <i class="fas fa-chevron-left text-gray-400 mx-2"></i>
                            <span class="text-gray-500 text-sm">الصلاحيات</span>
                        </div>
                    </li>
                </ol>
            </nav>
            <h1 class="text-2xl font-bold text-gray-900 flex items-center">
                <i class="fas fa-key mr-3"></i>
                إدارة الصلاحيات
            </h1>
            <p class="text-sm text-gray-600 mt-1">إدارة صلاحيات النظام وتجميعها</p>
        </div>
        <div class="flex gap-2">
            <button type="button" class="inline-flex items-center px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white text-sm font-medium rounded-md transition-colors duration-200" onclick="openModal('addPermissionModal')">
                <i class="fas fa-plus mr-2"></i>
                إضافة صلاحية
            </button>
            <button type="button" class="inline-flex items-center px-4 py-2 bg-green-600 hover:bg-green-700 text-white text-sm font-medium rounded-md transition-colors duration-200" onclick="openModal('bulkPermissionModal')">
                <i class="fas fa-layer-group mr-2"></i>
                إضافة مجموعة
            </button>
        </div>
    </div>
</div>

<!-- Main Content -->
<div class="bg-white rounded-lg shadow-sm border border-gray-200">
    <div class="px-6 py-4 border-b border-gray-200">
        <div class="flex justify-between items-center">
            <div>
                <h3 class="text-lg font-medium text-gray-900">قائمة الصلاحيات</h3>
                <p class="text-sm text-gray-600 mt-1">إدارة صلاحيات النظام وتجميعها</p>
            </div>
        </div>
    </div>
    <div class="p-6">
        <!-- DataTable Controls -->
        <div class="mb-6 flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
            <div class="flex flex-col sm:flex-row gap-4">
                <!-- Search -->
                <div class="relative">
                    <div class="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                        <i class="fas fa-search text-gray-400"></i>
                    </div>
                    <input type="text" id="globalSearch" placeholder="البحث في الصلاحيات..." class="w-full sm:w-64 pl-3 pr-10 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                </div>

                <!-- Group Filter -->
                <select id="groupFilter" class="px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                    <option value="">جميع المجموعات</option>
                    @foreach($groups as $group)
                        <option value="{{ $group }}">{{ ucfirst($group) }}</option>
                    @endforeach
                </select>

                <!-- Guard Filter -->
                <select id="guardFilter" class="px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                    <option value="">جميع الحراس</option>
                    <option value="web">ويب</option>
                    <option value="api">API</option>
                    <option value="sanctum">Sanctum</option>
                </select>
            </div>

            <!-- Export Buttons -->
            <div class="flex gap-2">
                <button type="button" id="exportExcel" class="px-3 py-2 bg-green-600 text-white text-sm font-medium rounded-md hover:bg-green-700 flex items-center gap-2">
                    <i class="fas fa-file-excel"></i>
                    Excel
                </button>
                <button type="button" id="exportPdf" class="px-3 py-2 bg-red-600 text-white text-sm font-medium rounded-md hover:bg-red-700 flex items-center gap-2">
                    <i class="fas fa-file-pdf"></i>
                    PDF
                </button>
                <button type="button" id="printTable" class="px-3 py-2 bg-gray-600 text-white text-sm font-medium rounded-md hover:bg-gray-700 flex items-center gap-2">
                    <i class="fas fa-print"></i>
                    طباعة
                </button>
            </div>
        </div>

        <!-- DataTable Container -->
        <div class="bg-white shadow-sm rounded-lg border border-gray-200 overflow-hidden">
            <div class="overflow-x-auto">
                <table id="permissionsTable" class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th class="px-6 py-4 text-right text-xs font-medium text-gray-500 uppercase tracking-wider border-b border-gray-200">#</th>
                            <th class="px-6 py-4 text-right text-xs font-medium text-gray-500 uppercase tracking-wider border-b border-gray-200">اسم الصلاحية</th>
                            <th class="px-6 py-4 text-right text-xs font-medium text-gray-500 uppercase tracking-wider border-b border-gray-200">المجموعة</th>
                            <th class="px-6 py-4 text-right text-xs font-medium text-gray-500 uppercase tracking-wider border-b border-gray-200">الحارس</th>
                            <th class="px-6 py-4 text-right text-xs font-medium text-gray-500 uppercase tracking-wider border-b border-gray-200">عدد الأدوار</th>
                            <th class="px-6 py-4 text-right text-xs font-medium text-gray-500 uppercase tracking-wider border-b border-gray-200">تاريخ الإنشاء</th>
                            <th class="px-6 py-4 text-right text-xs font-medium text-gray-500 uppercase tracking-wider border-b border-gray-200">الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                    </tbody>
                </table>
            </div>

            <!-- DataTable Info and Pagination -->
            <div class="bg-gray-50 px-6 py-3 border-t border-gray-200">
                <div class="flex items-center justify-between">
                    <div class="text-sm text-gray-700" id="tableInfo">
                        <!-- DataTable info will be inserted here -->
                    </div>
                    <div id="tablePagination">
                        <!-- DataTable pagination will be inserted here -->
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Add Permission Modal -->
<div id="addPermissionModal" class="fixed inset-0 bg-black bg-opacity-50 overflow-y-auto h-full w-full hidden z-50">
    <div class="relative top-10 mx-auto p-0 border-0 w-11/12 max-w-2xl shadow-2xl rounded-lg bg-white">
        <!-- Modal Header -->
        <div class="flex justify-between items-center px-6 py-4 bg-gradient-to-r from-blue-600 to-blue-700 text-white rounded-t-lg">
            <div class="flex items-center gap-3">
                <div class="w-8 h-8 bg-white bg-opacity-20 rounded-full flex items-center justify-center">
                    <i class="fas fa-key text-sm"></i>
                </div>
                <h3 class="text-lg font-semibold">إضافة صلاحية جديدة</h3>
            </div>
            <button type="button" class="text-white hover:text-gray-200 transition-colors duration-200" onclick="closeModal('addPermissionModal')">
                <i class="fas fa-times text-lg"></i>
            </button>
        </div>
        <!-- Modal Body -->
        <div class="px-6 py-6">
            <form id="addPermissionForm" action="{{ route('permissions.store') }}" method="POST">
                @csrf
                <div class="space-y-6">
                    <!-- Basic Information -->
                    <div class="bg-gray-50 p-4 rounded-lg">
                        <h4 class="text-sm font-medium text-gray-900 mb-4 flex items-center gap-2">
                            <i class="fas fa-info-circle text-blue-600"></i>
                            معلومات الصلاحية
                        </h4>
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div>
                                <label for="name" class="block text-sm font-medium text-gray-700 mb-2">اسم الصلاحية <span class="text-red-500">*</span></label>
                                <div class="relative">
                                    <div class="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                                        <i class="fas fa-key text-gray-400 text-sm"></i>
                                    </div>
                                    <input type="text" class="w-full pl-3 pr-10 py-3 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors duration-200" id="name" name="name" required placeholder="أدخل اسم الصلاحية">
                                </div>
                            </div>
                            <div>
                                <label for="group" class="block text-sm font-medium text-gray-700 mb-2">المجموعة <span class="text-red-500">*</span></label>
                                <div class="relative">
                                    <div class="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                                        <i class="fas fa-layer-group text-gray-400 text-sm"></i>
                                    </div>
                                    <input type="text" class="w-full pl-3 pr-10 py-3 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors duration-200" id="group" name="group" required placeholder="أدخل اسم المجموعة">
                                </div>
                            </div>
                        </div>
                        <div class="mt-4">
                            <label for="guard_name" class="block text-sm font-medium text-gray-700 mb-2">الحارس <span class="text-red-500">*</span></label>
                            <div class="relative">
                                <div class="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                                    <i class="fas fa-shield-alt text-gray-400 text-sm"></i>
                                </div>
                                <select class="w-full pl-3 pr-10 py-3 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors duration-200" id="guard_name" name="guard_name" required>
                                    <option value="web">ويب</option>
                                    <option value="api">API</option>
                                    <option value="sanctum">Sanctum</option>
                                </select>
                            </div>
                        </div>
                    </div>
                </div>
            </form>
        </div>

        <!-- Modal Footer -->
        <div class="px-6 py-4 bg-gray-50 border-t border-gray-200 rounded-b-lg">
            <div class="flex justify-end space-x-3">
                <button type="button" class="px-6 py-2 bg-gray-500 text-white text-sm font-medium rounded-lg hover:bg-gray-600 transition-colors duration-200 flex items-center gap-2" onclick="closeModal('addPermissionModal')">
                    <i class="fas fa-times"></i>
                    إلغاء
                </button>
                <button type="submit" form="addPermissionForm" class="px-6 py-2 bg-blue-600 text-white text-sm font-medium rounded-lg hover:bg-blue-700 transition-colors duration-200 flex items-center gap-2">
                    <i class="fas fa-save"></i>
                    حفظ الصلاحية
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Bulk Permission Modal -->
<div id="bulkPermissionModal" class="fixed inset-0 bg-black bg-opacity-50 overflow-y-auto h-full w-full hidden z-50">
    <div class="relative top-10 mx-auto p-0 border-0 w-11/12 max-w-3xl shadow-2xl rounded-lg bg-white">
        <!-- Modal Header -->
        <div class="flex justify-between items-center px-6 py-4 bg-gradient-to-r from-green-600 to-green-700 text-white rounded-t-lg">
            <div class="flex items-center gap-3">
                <div class="w-8 h-8 bg-white bg-opacity-20 rounded-full flex items-center justify-center">
                    <i class="fas fa-layer-group text-sm"></i>
                </div>
                <h3 class="text-lg font-semibold">إضافة مجموعة صلاحيات</h3>
            </div>
            <button type="button" class="text-white hover:text-gray-200 transition-colors duration-200" onclick="closeModal('bulkPermissionModal')">
                <i class="fas fa-times text-lg"></i>
            </button>
        </div>
        <!-- Modal Body -->
        <div class="px-6 py-6">
            <form id="bulkPermissionForm" action="{{ route('permissions.bulk-store') }}" method="POST">
                @csrf
                <div class="space-y-6">
                    <!-- Basic Information -->
                    <div class="bg-gray-50 p-4 rounded-lg">
                        <h4 class="text-sm font-medium text-gray-900 mb-4 flex items-center gap-2">
                            <i class="fas fa-info-circle text-green-600"></i>
                            معلومات المجموعة
                        </h4>
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div>
                                <label for="bulk_group" class="block text-sm font-medium text-gray-700 mb-2">اسم المجموعة <span class="text-red-500">*</span></label>
                                <div class="relative">
                                    <div class="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                                        <i class="fas fa-layer-group text-gray-400 text-sm"></i>
                                    </div>
                                    <input type="text" class="w-full pl-3 pr-10 py-3 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500 transition-colors duration-200" id="bulk_group" name="group" required placeholder="أدخل اسم المجموعة">
                                </div>
                            </div>
                            <div>
                                <label for="bulk_guard_name" class="block text-sm font-medium text-gray-700 mb-2">الحارس <span class="text-red-500">*</span></label>
                                <div class="relative">
                                    <div class="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                                        <i class="fas fa-shield-alt text-gray-400 text-sm"></i>
                                    </div>
                                    <select class="w-full pl-3 pr-10 py-3 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500 transition-colors duration-200" id="bulk_guard_name" name="guard_name" required>
                                        <option value="web">ويب</option>
                                        <option value="api">API</option>
                                        <option value="sanctum">Sanctum</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Permissions List -->
                    <div class="bg-gray-50 p-4 rounded-lg">
                        <h4 class="text-sm font-medium text-gray-900 mb-4 flex items-center gap-2">
                            <i class="fas fa-list text-green-600"></i>
                            قائمة الصلاحيات
                        </h4>
                        <div class="space-y-3">
                            <div class="flex items-center justify-between">
                                <label class="text-sm text-gray-700">الصلاحيات (واحدة في كل سطر):</label>
                                <button type="button" class="text-xs text-blue-600 hover:text-blue-800" onclick="addDefaultPermissions()">
                                    إضافة الصلاحيات الافتراضية
                                </button>
                            </div>
                            <textarea class="w-full px-3 py-3 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500 transition-colors duration-200" 
                                      id="permissions_list" name="permissions_list" rows="8" 
                                      placeholder="أدخل الصلاحيات، واحدة في كل سطر&#10;مثال:&#10;view&#10;create&#10;edit&#10;delete"></textarea>
                        </div>
                    </div>
                </div>
            </form>
        </div>

        <!-- Modal Footer -->
        <div class="px-6 py-4 bg-gray-50 border-t border-gray-200 rounded-b-lg">
            <div class="flex justify-end space-x-3">
                <button type="button" class="px-6 py-2 bg-gray-500 text-white text-sm font-medium rounded-lg hover:bg-gray-600 transition-colors duration-200 flex items-center gap-2" onclick="closeModal('bulkPermissionModal')">
                    <i class="fas fa-times"></i>
                    إلغاء
                </button>
                <button type="submit" form="bulkPermissionForm" class="px-6 py-2 bg-green-600 text-white text-sm font-medium rounded-lg hover:bg-green-700 transition-colors duration-200 flex items-center gap-2">
                    <i class="fas fa-save"></i>
                    حفظ المجموعة
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Edit Permission Modal -->
<div id="editPermissionModal" class="fixed inset-0 bg-black bg-opacity-50 overflow-y-auto h-full w-full hidden z-50">
    <div class="relative top-10 mx-auto p-0 border-0 w-11/12 max-w-2xl shadow-2xl rounded-lg bg-white">
        <!-- Modal Header -->
        <div class="flex justify-between items-center px-6 py-4 bg-gradient-to-r from-indigo-600 to-indigo-700 text-white rounded-t-lg">
            <div class="flex items-center gap-3">
                <div class="w-8 h-8 bg-white bg-opacity-20 rounded-full flex items-center justify-center">
                    <i class="fas fa-edit text-sm"></i>
                </div>
                <h3 class="text-lg font-semibold">تعديل الصلاحية</h3>
            </div>
            <button type="button" class="text-white hover:text-gray-200 transition-colors duration-200" onclick="closeModal('editPermissionModal')">
                <i class="fas fa-times text-lg"></i>
            </button>
        </div>
        <!-- Modal Body -->
        <div class="px-6 py-6">
            <form id="editPermissionForm" method="POST">
                @csrf
                @method('PUT')
                <div class="space-y-6">
                    <!-- Basic Information -->
                    <div class="bg-gray-50 p-4 rounded-lg">
                        <h4 class="text-sm font-medium text-gray-900 mb-4 flex items-center gap-2">
                            <i class="fas fa-info-circle text-indigo-600"></i>
                            معلومات الصلاحية
                        </h4>
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div>
                                <label for="edit_name" class="block text-sm font-medium text-gray-700 mb-2">اسم الصلاحية <span class="text-red-500">*</span></label>
                                <div class="relative">
                                    <div class="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                                        <i class="fas fa-key text-gray-400 text-sm"></i>
                                    </div>
                                    <input type="text" class="w-full pl-3 pr-10 py-3 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 transition-colors duration-200" id="edit_name" name="name" required>
                                </div>
                            </div>
                            <div>
                                <label for="edit_group" class="block text-sm font-medium text-gray-700 mb-2">المجموعة <span class="text-red-500">*</span></label>
                                <div class="relative">
                                    <div class="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                                        <i class="fas fa-layer-group text-gray-400 text-sm"></i>
                                    </div>
                                    <input type="text" class="w-full pl-3 pr-10 py-3 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 transition-colors duration-200" id="edit_group" name="group" required>
                                </div>
                            </div>
                        </div>
                        <div class="mt-4">
                            <label for="edit_guard_name" class="block text-sm font-medium text-gray-700 mb-2">الحارس <span class="text-red-500">*</span></label>
                            <div class="relative">
                                <div class="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                                    <i class="fas fa-shield-alt text-gray-400 text-sm"></i>
                                </div>
                                <select class="w-full pl-3 pr-10 py-3 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 transition-colors duration-200" id="edit_guard_name" name="guard_name" required>
                                    <option value="web">ويب</option>
                                    <option value="api">API</option>
                                    <option value="sanctum">Sanctum</option>
                                </select>
                            </div>
                        </div>
                    </div>
                </div>
            </form>
        </div>

        <!-- Modal Footer -->
        <div class="px-6 py-4 bg-gray-50 border-t border-gray-200 rounded-b-lg">
            <div class="flex justify-end space-x-3">
                <button type="button" class="px-6 py-2 bg-gray-500 text-white text-sm font-medium rounded-lg hover:bg-gray-600 transition-colors duration-200 flex items-center gap-2" onclick="closeModal('editPermissionModal')">
                    <i class="fas fa-times"></i>
                    إلغاء
                </button>
                <button type="submit" form="editPermissionForm" class="px-6 py-2 bg-indigo-600 text-white text-sm font-medium rounded-lg hover:bg-indigo-700 transition-colors duration-200 flex items-center gap-2">
                    <i class="fas fa-save"></i>
                    تحديث الصلاحية
                </button>
            </div>
        </div>
    </div>
</div>

@endsection

@push('scripts')
<!-- DataTables CDN -->
<link rel="stylesheet" type="text/css" href="https://cdn.datatables.net/1.11.5/css/dataTables.bootstrap4.min.css">
<script type="text/javascript" charset="utf8" src="https://cdn.datatables.net/1.11.5/js/jquery.dataTables.js"></script>
<script type="text/javascript" charset="utf8" src="https://cdn.datatables.net/1.11.5/js/dataTables.bootstrap4.min.js"></script>
<!-- Sweet Alert -->
<script src="{{URL::asset('assets/plugins/sweet-alert/sweetalert.min.js')}}"></script>

<script>
// Modal Functions
function openModal(modalId) {
    document.getElementById(modalId).classList.remove('hidden');
    document.body.style.overflow = 'hidden';
}

function closeModal(modalId) {
    document.getElementById(modalId).classList.add('hidden');
    document.body.style.overflow = 'auto';
}

// Add default permissions function
function addDefaultPermissions() {
    const defaultPermissions = 'view\ncreate\nedit\ndelete\nmanage';
    $('#permissions_list').val(defaultPermissions);
}

$(document).ready(function() {
    // Initialize DataTable
    var table = $('#permissionsTable').DataTable({
        processing: true,
        serverSide: true,
        ajax: {
            url: "{{ route('permissions.data') }}",
            data: function (d) {
                d.group = $('#groupFilter').val();
                d.guard_name = $('#guardFilter').val();
                d.search = $('#globalSearch').val();
            }
        },
        columns: [
            {data: 'DT_RowIndex', name: 'DT_RowIndex', orderable: false, searchable: false},
            {data: 'name', name: 'name'},
            {data: 'group', name: 'group'},
            {data: 'guard_badge', name: 'guard_name'},
            {data: 'roles_count', name: 'roles_count', orderable: false},
            {data: 'created_at', name: 'created_at'},
            {data: 'action', name: 'action', orderable: false, searchable: false}
        ],
        dom: 'rt<"flex justify-between items-center mt-4"<"flex items-center gap-2"l><"flex items-center gap-2"ip>>',
        pageLength: 25,
        responsive: true,
        language: {
            url: '//cdn.datatables.net/plug-ins/1.10.25/i18n/Arabic.json'
        },
        initComplete: function() {
            // Update info and pagination styling
            $('.dataTables_info').addClass('text-sm text-gray-700');
            $('.dataTables_paginate').addClass('pagination-container');
            $('.dataTables_length select').addClass('px-3 py-1 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500');
        }
    });

    // Filter functionality
    $('#groupFilter, #guardFilter').change(function() {
        table.draw();
    });

    // Global search functionality
    $('#globalSearch').on('keyup', function() {
        table.draw();
    });

    // Handle Add Permission Form Submission
    $('#addPermissionForm').on('submit', function(e) {
        e.preventDefault();

        // Show loading state
        const submitBtn = $(this).find('button[type="submit"]');
        const originalText = submitBtn.html();
        submitBtn.html('<i class="fas fa-spinner fa-spin"></i> جاري الحفظ...').prop('disabled', true);

        $.ajax({
            url: $(this).attr('action'),
            type: 'POST',
            data: $(this).serialize(),
            success: function(response) {
                closeModal('addPermissionModal');
                $('#addPermissionForm')[0].reset();
                swal("تم الحفظ!", "تم إضافة الصلاحية بنجاح.", "success");
                table.draw();
            },
            error: function(xhr) {
                var errors = xhr.responseJSON.errors;
                var errorMessage = 'حدث خطأ أثناء إضافة الصلاحية:\n';
                if (errors) {
                    Object.keys(errors).forEach(function(key) {
                        errorMessage += errors[key][0] + '\n';
                    });
                }
                swal("خطأ!", errorMessage, "error");
            },
            complete: function() {
                submitBtn.html(originalText).prop('disabled', false);
            }
        });
    });

    // Handle Bulk Permission Form Submission
    $('#bulkPermissionForm').on('submit', function(e) {
        e.preventDefault();

        // Show loading state
        const submitBtn = $(this).find('button[type="submit"]');
        const originalText = submitBtn.html();
        submitBtn.html('<i class="fas fa-spinner fa-spin"></i> جاري الحفظ...').prop('disabled', true);

        var permissions = $('#permissions_list').val().split('\n').filter(function(line) {
            return line.trim() !== '';
        });

        $.ajax({
            url: $(this).attr('action'),
            type: 'POST',
            data: {
                _token: $('input[name="_token"]').val(),
                group: $('#bulk_group').val(),
                guard_name: $('#bulk_guard_name').val(),
                permissions_list: $('#permissions_list').val()
            },
            success: function(response) {
                closeModal('bulkPermissionModal');
                $('#bulkPermissionForm')[0].reset();
                swal("تم الحفظ!", "تم إنشاء الصلاحيات بنجاح.", "success");
                table.draw();
            },
            error: function(xhr) {
                var errors = xhr.responseJSON.errors;
                var errorMessage = 'حدث خطأ أثناء إنشاء الصلاحيات:\n';
                if (errors) {
                    Object.keys(errors).forEach(function(key) {
                        errorMessage += errors[key][0] + '\n';
                    });
                }
                swal("خطأ!", errorMessage, "error");
            },
            complete: function() {
                submitBtn.html(originalText).prop('disabled', false);
            }
        });
    });

    // Handle Edit Permission Form Submission
    $('#editPermissionForm').on('submit', function(e) {
        e.preventDefault();

        // Show loading state
        const submitBtn = $(this).find('button[type="submit"]');
        const originalText = submitBtn.html();
        submitBtn.html('<i class="fas fa-spinner fa-spin"></i> جاري التحديث...').prop('disabled', true);

        $.ajax({
            url: $(this).attr('action'),
            type: 'PUT',
            data: $(this).serialize(),
            success: function(response) {
                closeModal('editPermissionModal');
                swal("تم التحديث!", "تم تحديث الصلاحية بنجاح.", "success");
                table.draw();
            },
            error: function(xhr) {
                var errors = xhr.responseJSON.errors;
                var errorMessage = 'حدث خطأ أثناء تحديث الصلاحية:\n';
                if (errors) {
                    Object.keys(errors).forEach(function(key) {
                        errorMessage += errors[key][0] + '\n';
                    });
                }
                swal("خطأ!", errorMessage, "error");
            },
            complete: function() {
                submitBtn.html(originalText).prop('disabled', false);
            }
        });
    });

    // Export Functions
    $('#exportExcel').on('click', function() {
        window.location.href = "{{ route('permissions.export', 'excel') }}";
    });

    $('#exportPdf').on('click', function() {
        window.location.href = "{{ route('permissions.export', 'pdf') }}";
    });

    $('#printTable').on('click', function() {
        window.print();
    });

    // Edit permission
    window.editPermission = function(id) {
        $.get("{{ url('admin/permissions') }}/" + id, function(data) {
            $('#edit_name').val(data.name);
            $('#edit_group').val(data.group || '');
            $('#edit_guard_name').val(data.guard_name);
            $('#editPermissionForm').attr('action', "{{ url('admin/permissions') }}/" + id);
            openModal('editPermissionModal');
        }).fail(function() {
            swal('خطأ!', 'حدث خطأ أثناء تحميل بيانات الصلاحية', 'error');
        });
    };

    // Delete permission
    window.deletePermission = function(id) {
        swal({
            title: "هل أنت متأكد؟",
            text: "لن تتمكن من التراجع عن هذا الإجراء!",
            type: "warning",
            showCancelButton: true,
            confirmButtonColor: "#dc2626",
            cancelButtonColor: "#6b7280",
            confirmButtonText: "نعم، احذف!",
            cancelButtonText: "إلغاء",
            closeOnConfirm: false,
            closeOnCancel: false
        }, function(isConfirm) {
            if (isConfirm) {
                $.ajax({
                    url: "{{ url('admin/permissions') }}/" + id,
                    type: 'DELETE',
                    data: {
                        _token: "{{ csrf_token() }}"
                    },
                    success: function(response) {
                        swal("تم الحذف!", "تم حذف الصلاحية بنجاح.", "success");
                        table.draw();
                    },
                    error: function(xhr) {
                        swal("خطأ!", "حدث خطأ أثناء حذف الصلاحية.", "error");
                    }
                });
            } else {
                swal("تم الإلغاء", "لم يتم حذف الصلاحية.", "error");
            }
        });
    };
});

// Close modal when clicking outside
$(document).on('click', '.fixed.inset-0', function(e) {
    if (e.target === this) {
        const modalId = $(this).attr('id');
        closeModal(modalId);
    }
});

// Close modal with Escape key
$(document).keydown(function(e) {
    if (e.key === "Escape") {
        $('.fixed.inset-0:not(.hidden)').each(function() {
            closeModal($(this).attr('id'));
        });
    }
});
</script>
@endpush