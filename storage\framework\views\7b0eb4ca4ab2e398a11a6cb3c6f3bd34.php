<?php $__env->startPush('styles'); ?>
<meta name="csrf-token" content="<?php echo e(csrf_token()); ?>">
<!-- DataTables CSS -->
<link rel="stylesheet" href="https://cdn.datatables.net/1.13.7/css/jquery.dataTables.min.css">
<link rel="stylesheet" href="https://cdn.datatables.net/buttons/2.4.2/css/buttons.dataTables.min.css">
<link rel="stylesheet" href="https://cdn.datatables.net/responsive/2.5.0/css/responsive.dataTables.min.css">
<!-- Select2 CSS -->
<link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet" />
<!-- SweetAlert2 CSS -->
<link href="https://cdn.jsdelivr.net/npm/sweetalert2@11.7.32/dist/sweetalert2.min.css" rel="stylesheet">
<style>
    /* Custom DataTables Tailwind Styling */
    .dataTables_wrapper {
        font-family: inherit;
    }
    
    .dataTables_wrapper .dataTables_length,
    .dataTables_wrapper .dataTables_filter,
    .dataTables_wrapper .dataTables_info,
    .dataTables_wrapper .dataTables_processing,
    .dataTables_wrapper .dataTables_paginate {
        color: #374151;
        font-size: 0.875rem;
        padding: 0.5rem 0.75rem;
    }
    .dataTables_processing{
        margin: auto
    }
    .dataTables_wrapper .dataTables_length select,
    .dataTables_wrapper .dataTables_filter input {
        border: 1px solid #d1d5db;
        border-radius: 0.375rem;
        padding: 0.375rem 0.75rem;
        font-size: 0.875rem;
    }
    
    .dataTables_wrapper .dataTables_filter input:focus {
        outline: none;
        border-color: #3b82f6;
        box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
    }
    
    .dataTables_wrapper .dataTables_paginate .paginate_button {
        padding: 0.5rem 0.75rem !important;
        margin: 0 0.125rem !important;
        border-radius: 0.375rem !important;
        border: 1px solid #d1d5db !important;
        background: white !important;
        color: #374151 !important;
        text-decoration: none !important;
    }
    
    .dataTables_wrapper .dataTables_paginate .paginate_button:hover {
        background: #f3f4f6 !important;
        border-color: #9ca3af !important;
        color: #374151 !important;
    }
    
    .dataTables_wrapper .dataTables_paginate .paginate_button.current {
        background: #3b82f6 !important;
        border-color: #3b82f6 !important;
        color: white !important;
    }
    
    .dataTables_wrapper .dataTables_paginate .paginate_button.disabled {
        background: #f9fafb !important;
        color: #9ca3af !important;
        border-color: #e5e7eb !important;
    }
    
    table.dataTable {
        border-collapse: separate !important;
        border-spacing: 0 !important;
    }
    
    table.dataTable thead th {
        background-color: #f9fafb;
        border-bottom: 1px solid #e5e7eb;
        font-weight: 600;
        color: #374151;
        padding: 0.75rem;
    }
    
    table.dataTable tbody td {
        padding: 0.75rem;
        border-bottom: 1px solid #f3f4f6;
    }
    
    table.dataTable tbody tr:hover {
        background-color: #f9fafb;
    }
    
    .select2-container--default .select2-selection--single {
        height: 42px;
        border: 1px solid #d1d5db;
        border-radius: 0.5rem;
        padding: 0.5rem;
    }
    
    .select2-container--default .select2-selection--single .select2-selection__rendered {
        line-height: 26px;
        padding-left: 0;
    }
    
    .select2-container--default .select2-selection--single .select2-selection__arrow {
        height: 40px;
    }
    
    .modal-backdrop {
        background-color: rgba(0, 0, 0, 0.5);
    }
    
    .animate-fadeIn {
        animation: fadeIn 0.3s ease-in-out;
    }
    
    @keyframes fadeIn {
        from { opacity: 0; transform: scale(0.95); }
        to { opacity: 1; transform: scale(1); }
    }
    
    .animate-slideDown {
        animation: slideDown 0.3s ease-in-out;
    }
    
    @keyframes slideDown {
        from { opacity: 0; transform: translateY(-10px); }
        to { opacity: 1; transform: translateY(0); }
    }
</style>
<?php $__env->stopPush(); ?>

<?php $__env->startSection('content'); ?>
<!-- Page Header -->
<div class="mb-6">
    <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between">
        <div class="mb-4 sm:mb-0">
            <nav class="flex mb-2" aria-label="Breadcrumb">
                <ol class="inline-flex items-center space-x-1 md:space-x-3">
                    <li class="inline-flex items-center">
                        <span class="text-gray-700 text-sm font-medium">إدارة النظام</span>
                    </li>
                    <li>
                        <div class="flex items-center">
                            <i class="fas fa-chevron-left text-gray-400 mx-2"></i>
                            <span class="text-gray-500 text-sm">المستخدمين</span>
                        </div>
                    </li>
                </ol>
            </nav>
            <h1 class="text-2xl font-bold text-gray-900 flex items-center">
                <i class="fas fa-users mr-3"></i>
                إدارة المستخدمين
            </h1>
            <p class="text-sm text-gray-600 mt-1">إدارة مستخدمي النظام وصلاحياتهم</p>
        </div>
        <div>
            <button type="button" class="inline-flex items-center px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white text-sm font-medium rounded-md transition-colors duration-200" onclick="openModal('addUserModal')">
                <i class="fas fa-plus mr-2"></i>
                إضافة مستخدم جديد
            </button>
        </div>
    </div>
</div>

<!-- Main Content -->
<div class="bg-white rounded-lg shadow-sm border border-gray-200">
    <div class="px-6 py-4 border-b border-gray-200">
        <div class="flex justify-between items-center">
            <div>
                <h3 class="text-lg font-medium text-gray-900">قائمة المستخدمين</h3>
                <p class="text-sm text-gray-600 mt-1">إدارة مستخدمي النظام وصلاحياتهم</p>
            </div>
        </div>
    </div>
    <div class="p-6">
        <!-- DataTable Controls -->
        <div class="mb-6 flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
            <div class="flex flex-col sm:flex-row gap-4">
                <!-- Search -->
                <div class="relative">
                    <div class="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                        <i class="fas fa-search text-gray-400"></i>
                    </div>
                    <input type="text" id="globalSearch" placeholder="البحث في المستخدمين..." class="w-full sm:w-64 pl-3 pr-10 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                </div>

                <!-- Tenant Filter -->
                <select id="tenantFilter" class="px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                    <option value="">جميع المستأجرين</option>
                    <?php $__currentLoopData = $tenants; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $tenant): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <option value="<?php echo e($tenant->id); ?>"><?php echo e($tenant->name); ?></option>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </select>

                <!-- Branch Filter -->
                <select id="branchFilter" class="px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                    <option value="">جميع الفروع</option>
                    <?php $__currentLoopData = $branches; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $branch): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <option value="<?php echo e($branch->id); ?>"><?php echo e($branch->name); ?></option>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </select>

                <!-- Role Filter -->
                <select id="roleFilter" class="px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                    <option value="">جميع الأدوار</option>
                    <?php $__currentLoopData = $roles; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $role): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <option value="<?php echo e($role->name); ?>"><?php echo e($role->name); ?></option>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </select>

                <!-- Status Filter -->
                <select id="statusFilter" class="px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                    <option value="">جميع الحالات</option>
                    <option value="1">نشط</option>
                    <option value="0">غير نشط</option>
                </select>
            </div>

            <!-- Export Buttons -->
            <div class="flex gap-2">
                <button type="button" id="exportExcel" class="px-3 py-2 bg-green-600 text-white text-sm font-medium rounded-md hover:bg-green-700 flex items-center gap-2">
                    <i class="fas fa-file-excel"></i>
                    Excel
                </button>
                <button type="button" id="exportPdf" class="px-3 py-2 bg-red-600 text-white text-sm font-medium rounded-md hover:bg-red-700 flex items-center gap-2">
                    <i class="fas fa-file-pdf"></i>
                    PDF
                </button>
            </div>
        </div>

        <!-- DataTable -->
        <div class="overflow-x-auto">
            <table id="usersTable" class="min-w-full divide-y divide-gray-200">
                <thead>
                    <tr>
                        <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">#</th>
                        <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">الاسم</th>
                        <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">البريد الإلكتروني</th>
                        <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">المستأجر</th>
                        <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">الفرع</th>
                        <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">الراتب</th>
                        <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">الأدوار</th>
                        <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">الحالة</th>
                        <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">الإجراءات</th>
                    </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200">
                    <!-- Data will be loaded via DataTables -->
                </tbody>
            </table>
        </div>
    </div>
</div>

<!-- Add User Modal -->
<div id="addUserModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden z-50">
    <div class="relative top-20 mx-auto p-5 border w-11/12 md:w-3/4 lg:w-1/2 shadow-lg rounded-md bg-white animate-fadeIn">
        <div class="mt-3">
            <div class="flex items-center justify-between mb-4">
                <h3 class="text-lg font-medium text-gray-900 flex items-center">
                    <i class="fas fa-user-plus mr-2"></i>
                    إضافة مستخدم جديد
                </h3>
                <button type="button" class="text-gray-400 hover:text-gray-600" onclick="closeModal('addUserModal')">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            
            <form id="addUserForm" class="space-y-4">
                <?php echo csrf_field(); ?>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <!-- Name -->
                    <div>
                        <label for="add_name" class="block text-sm font-medium text-gray-700 mb-1">الاسم *</label>
                        <input type="text" id="add_name" name="name" required class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                        <div class="text-red-500 text-sm mt-1 hidden" id="add_name_error"></div>
                    </div>

                    <!-- Email -->
                    <div>
                        <label for="add_email" class="block text-sm font-medium text-gray-700 mb-1">البريد الإلكتروني *</label>
                        <input type="email" id="add_email" name="email" required class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                        <div class="text-red-500 text-sm mt-1 hidden" id="add_email_error"></div>
                    </div>

                    <!-- Password -->
                    <div>
                        <label for="add_password" class="block text-sm font-medium text-gray-700 mb-1">كلمة المرور *</label>
                        <input type="password" id="add_password" name="password" required class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                        <div class="text-red-500 text-sm mt-1 hidden" id="add_password_error"></div>
                    </div>

                    <!-- Password Confirmation -->
                    <div>
                        <label for="add_password_confirmation" class="block text-sm font-medium text-gray-700 mb-1">تأكيد كلمة المرور *</label>
                        <input type="password" id="add_password_confirmation" name="password_confirmation" required class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                        <div class="text-red-500 text-sm mt-1 hidden" id="add_password_confirmation_error"></div>
                    </div>

                    <!-- Tenant -->
                    <div>
                        <label for="add_tenant_id" class="block text-sm font-medium text-gray-700 mb-1">المستأجر *</label>
                        <select id="add_tenant_id" name="tenant_id" required class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                            <option value="">اختر المستأجر</option>
                            <?php $__currentLoopData = $tenants; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $tenant): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <option value="<?php echo e($tenant->id); ?>"><?php echo e($tenant->name); ?></option>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </select>
                        <div class="text-red-500 text-sm mt-1 hidden" id="add_tenant_id_error"></div>
                    </div>

                    <!-- Branch -->
                    <div>
                        <label for="add_branch_id" class="block text-sm font-medium text-gray-700 mb-1">الفرع</label>
                        <select id="add_branch_id" name="branch_id" class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                            <option value="">اختر الفرع</option>
                            <?php $__currentLoopData = $branches; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $branch): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <option value="<?php echo e($branch->id); ?>"><?php echo e($branch->name); ?></option>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </select>
                        <div class="text-red-500 text-sm mt-1 hidden" id="add_branch_id_error"></div>
                    </div>

                    <!-- Employee ID -->
                    <div>
                        <label for="add_employee_id" class="block text-sm font-medium text-gray-700 mb-1">رقم الموظف</label>
                        <input type="text" id="add_employee_id" name="employee_id" class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                        <div class="text-red-500 text-sm mt-1 hidden" id="add_employee_id_error"></div>
                    </div>

                    <!-- Position -->
                    <div>
                        <label for="add_position" class="block text-sm font-medium text-gray-700 mb-1">المنصب</label>
                        <input type="text" id="add_position" name="position" class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                        <div class="text-red-500 text-sm mt-1 hidden" id="add_position_error"></div>
                    </div>

                    <!-- Department -->
                    <div>
                        <label for="add_department" class="block text-sm font-medium text-gray-700 mb-1">القسم</label>
                        <input type="text" id="add_department" name="department" class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                        <div class="text-red-500 text-sm mt-1 hidden" id="add_department_error"></div>
                    </div>

                    <!-- Hourly Rate -->
                    <div>
                        <label for="add_hourly_rate" class="block text-sm font-medium text-gray-700 mb-1">الأجر بالساعة</label>
                        <input type="number" step="0.01" id="add_hourly_rate" name="hourly_rate" class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                        <div class="text-red-500 text-sm mt-1 hidden" id="add_hourly_rate_error"></div>
                    </div>

                    <!-- Salary -->
                    <div>
                        <label for="add_salary" class="block text-sm font-medium text-gray-700 mb-1">الراتب</label>
                        <input type="number" step="0.01" id="add_salary" name="salary" class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                        <div class="text-red-500 text-sm mt-1 hidden" id="add_salary_error"></div>
                    </div>

                    <!-- Base Salary -->
                    <div>
                        <label for="add_base_salary" class="block text-sm font-medium text-gray-700 mb-1">الراتب الأساسي</label>
                        <input type="number" step="0.01" id="add_base_salary" name="base_salary" class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                        <div class="text-red-500 text-sm mt-1 hidden" id="add_base_salary_error"></div>
                    </div>
                </div>

                <!-- Roles -->
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">الأدوار</label>
                    <div class="grid grid-cols-2 md:grid-cols-3 gap-2">
                        <?php $__currentLoopData = $roles; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $role): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <label class="flex items-center">
                                <input type="checkbox" name="roles[]" value="<?php echo e($role->name); ?>" class="rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50">
                                <span class="mr-2 text-sm text-gray-700"><?php echo e($role->name); ?></span>
                            </label>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </div>
                    <div class="text-red-500 text-sm mt-1 hidden" id="add_roles_error"></div>
                </div>

                <div class="flex justify-end space-x-2 pt-4">
                    <button type="button" class="px-4 py-2 bg-gray-300 text-gray-700 rounded-md hover:bg-gray-400 transition-colors duration-200" onclick="closeModal('addUserModal')">
                        إلغاء
                    </button>
                    <button type="submit" class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors duration-200">
                        <i class="fas fa-save mr-1"></i>
                        حفظ
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Edit User Modal -->
<div id="editUserModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden z-50">
    <div class="relative top-20 mx-auto p-5 border w-11/12 md:w-3/4 lg:w-1/2 shadow-lg rounded-md bg-white animate-fadeIn">
        <div class="mt-3">
            <div class="flex items-center justify-between mb-4">
                <h3 class="text-lg font-medium text-gray-900 flex items-center">
                    <i class="fas fa-user-edit mr-2"></i>
                    تعديل المستخدم
                </h3>
                <button type="button" class="text-gray-400 hover:text-gray-600" onclick="closeModal('editUserModal')">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            
            <form id="editUserForm" class="space-y-4">
                <?php echo csrf_field(); ?>
                <?php echo method_field('PUT'); ?>
                <input type="hidden" id="edit_user_id" name="user_id">
                
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <!-- Name -->
                    <div>
                        <label for="edit_name" class="block text-sm font-medium text-gray-700 mb-1">الاسم *</label>
                        <input type="text" id="edit_name" name="name" required class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                        <div class="text-red-500 text-sm mt-1 hidden" id="edit_name_error"></div>
                    </div>

                    <!-- Email -->
                    <div>
                        <label for="edit_email" class="block text-sm font-medium text-gray-700 mb-1">البريد الإلكتروني *</label>
                        <input type="email" id="edit_email" name="email" required class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                        <div class="text-red-500 text-sm mt-1 hidden" id="edit_email_error"></div>
                    </div>

                    <!-- Password -->
                    <div>
                        <label for="edit_password" class="block text-sm font-medium text-gray-700 mb-1">كلمة المرور الجديدة</label>
                        <input type="password" id="edit_password" name="password" class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                        <div class="text-red-500 text-sm mt-1 hidden" id="edit_password_error"></div>
                        <p class="text-xs text-gray-500 mt-1">اتركه فارغاً إذا كنت لا تريد تغيير كلمة المرور</p>
                    </div>

                    <!-- Password Confirmation -->
                    <div>
                        <label for="edit_password_confirmation" class="block text-sm font-medium text-gray-700 mb-1">تأكيد كلمة المرور</label>
                        <input type="password" id="edit_password_confirmation" name="password_confirmation" class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                        <div class="text-red-500 text-sm mt-1 hidden" id="edit_password_confirmation_error"></div>
                    </div>

                    <!-- Tenant -->
                    <div>
                        <label for="edit_tenant_id" class="block text-sm font-medium text-gray-700 mb-1">المستأجر *</label>
                        <select id="edit_tenant_id" name="tenant_id" required class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                            <option value="">اختر المستأجر</option>
                            <?php $__currentLoopData = $tenants; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $tenant): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <option value="<?php echo e($tenant->id); ?>"><?php echo e($tenant->name); ?></option>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </select>
                        <div class="text-red-500 text-sm mt-1 hidden" id="edit_tenant_id_error"></div>
                    </div>

                    <!-- Branch -->
                    <div>
                        <label for="edit_branch_id" class="block text-sm font-medium text-gray-700 mb-1">الفرع</label>
                        <select id="edit_branch_id" name="branch_id" class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                            <option value="">اختر الفرع</option>
                            <?php $__currentLoopData = $branches; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $branch): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <option value="<?php echo e($branch->id); ?>"><?php echo e($branch->name); ?></option>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </select>
                        <div class="text-red-500 text-sm mt-1 hidden" id="edit_branch_id_error"></div>
                    </div>

                    <!-- Employee ID -->
                    <div>
                        <label for="edit_employee_id" class="block text-sm font-medium text-gray-700 mb-1">رقم الموظف</label>
                        <input type="text" id="edit_employee_id" name="employee_id" class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                        <div class="text-red-500 text-sm mt-1 hidden" id="edit_employee_id_error"></div>
                    </div>

                    <!-- Position -->
                    <div>
                        <label for="edit_position" class="block text-sm font-medium text-gray-700 mb-1">المنصب</label>
                        <input type="text" id="edit_position" name="position" class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                        <div class="text-red-500 text-sm mt-1 hidden" id="edit_position_error"></div>
                    </div>

                    <!-- Department -->
                    <div>
                        <label for="edit_department" class="block text-sm font-medium text-gray-700 mb-1">القسم</label>
                        <input type="text" id="edit_department" name="department" class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                        <div class="text-red-500 text-sm mt-1 hidden" id="edit_department_error"></div>
                    </div>

                    <!-- Hourly Rate -->
                    <div>
                        <label for="edit_hourly_rate" class="block text-sm font-medium text-gray-700 mb-1">الأجر بالساعة</label>
                        <input type="number" step="0.01" id="edit_hourly_rate" name="hourly_rate" class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                        <div class="text-red-500 text-sm mt-1 hidden" id="edit_hourly_rate_error"></div>
                    </div>

                    <!-- Salary -->
                    <div>
                        <label for="edit_salary" class="block text-sm font-medium text-gray-700 mb-1">الراتب</label>
                        <input type="number" step="0.01" id="edit_salary" name="salary" class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                        <div class="text-red-500 text-sm mt-1 hidden" id="edit_salary_error"></div>
                    </div>

                    <!-- Base Salary -->
                    <div>
                        <label for="edit_base_salary" class="block text-sm font-medium text-gray-700 mb-1">الراتب الأساسي</label>
                        <input type="number" step="0.01" id="edit_base_salary" name="base_salary" class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                        <div class="text-red-500 text-sm mt-1 hidden" id="edit_base_salary_error"></div>
                    </div>
                </div>

                <!-- Roles -->
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">الأدوار</label>
                    <div class="grid grid-cols-2 md:grid-cols-3 gap-2" id="edit_roles_container">
                        <?php $__currentLoopData = $roles; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $role): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <label class="flex items-center">
                                <input type="checkbox" name="roles[]" value="<?php echo e($role->name); ?>" class="rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50">
                                <span class="mr-2 text-sm text-gray-700"><?php echo e($role->name); ?></span>
                            </label>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </div>
                    <div class="text-red-500 text-sm mt-1 hidden" id="edit_roles_error"></div>
                </div>

                <div class="flex justify-end space-x-2 pt-4">
                    <button type="button" class="px-4 py-2 bg-gray-300 text-gray-700 rounded-md hover:bg-gray-400 transition-colors duration-200" onclick="closeModal('editUserModal')">
                        إلغاء
                    </button>
                    <button type="submit" class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors duration-200">
                        <i class="fas fa-save mr-1"></i>
                        تحديث
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>



<!-- Assign Roles Modal -->
<div id="assignRolesModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden z-50">
    <div class="relative top-20 mx-auto p-5 border w-11/12 md:w-2/3 lg:w-1/2 shadow-lg rounded-md bg-white animate-fadeIn">
        <div class="mt-3">
            <div class="flex items-center justify-between mb-4">
                <h3 class="text-lg font-medium text-gray-900 flex items-center">
                    <i class="fas fa-users mr-2"></i>
                    تعيين الأدوار
                </h3>
                <button type="button" class="text-gray-400 hover:text-gray-600" onclick="closeModal('assignRolesModal')">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            
            <form id="assignRolesForm" class="space-y-4">
                <?php echo csrf_field(); ?>
                <input type="hidden" id="assign_user_id" name="user_id">
                
                <div class="mb-4">
                    <p class="text-sm text-gray-600">المستخدم: <span id="assign_user_name" class="font-medium"></span></p>
                </div>

                <!-- Roles -->
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">الأدوار المتاحة</label>
                    <div class="grid grid-cols-2 md:grid-cols-3 gap-2" id="assign_roles_container">
                        <?php $__currentLoopData = $roles; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $role): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <label class="flex items-center">
                                <input type="checkbox" name="roles[]" value="<?php echo e($role->name); ?>" class="rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50">
                                <span class="mr-2 text-sm text-gray-700"><?php echo e($role->name); ?></span>
                            </label>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </div>
                    <div class="text-red-500 text-sm mt-1 hidden" id="assign_roles_error"></div>
                </div>

                <div class="flex justify-end space-x-2 pt-4">
                    <button type="button" class="px-4 py-2 bg-gray-300 text-gray-700 rounded-md hover:bg-gray-400 transition-colors duration-200" onclick="closeModal('assignRolesModal')">
                        إلغاء
                    </button>
                    <button type="submit" class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors duration-200">
                        <i class="fas fa-save mr-1"></i>
                        حفظ الأدوار
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<?php $__env->stopSection(); ?>

<?php $__env->startPush('scripts'); ?>
<!-- jQuery -->
<script src="https://code.jquery.com/jquery-3.7.1.min.js"></script>
<!-- DataTables JS -->
<script src="https://cdn.datatables.net/1.13.7/js/jquery.dataTables.min.js"></script>
<script src="https://cdn.datatables.net/buttons/2.4.2/js/dataTables.buttons.min.js"></script>
<script src="https://cdn.datatables.net/buttons/2.4.2/js/buttons.html5.min.js"></script>
<script src="https://cdn.datatables.net/buttons/2.4.2/js/buttons.print.min.js"></script>
<script src="https://cdn.datatables.net/responsive/2.5.0/js/dataTables.responsive.min.js"></script>
<!-- Select2 JS -->
<script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>
<!-- JSZip for Excel export -->
<script src="https://cdnjs.cloudflare.com/ajax/libs/jszip/3.10.1/jszip.min.js"></script>
<!-- pdfmake for PDF export -->
<script src="https://cdnjs.cloudflare.com/ajax/libs/pdfmake/0.2.7/pdfmake.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/pdfmake/0.2.7/vfs_fonts.js"></script>
<!-- SweetAlert2 JS -->
<script src="https://cdn.jsdelivr.net/npm/sweetalert2@11.7.32/dist/sweetalert2.all.min.js"></script>

<script>
$(document).ready(function() {
    // Initialize DataTable
    var table = $('#usersTable').DataTable({
        processing: true,
        serverSide: true,
        ajax: {
            url: '<?php echo e(route("users.data")); ?>',
            data: function(d) {
                d.tenant_id = $('#tenantFilter').val();
                d.branch_id = $('#branchFilter').val();
                d.role = $('#roleFilter').val();
                d.is_active = $('#statusFilter').val();
            }
        },
        columns: [
            { data: 'DT_RowIndex', name: 'DT_RowIndex', orderable: false, searchable: false },
            { data: 'name', name: 'name' },
            { data: 'email', name: 'email' },
            { data: 'tenant_name', name: 'tenant.name' },
            { data: 'branch_name', name: 'branch.name' },
            { data: 'salary', name: 'salary' },
            { data: 'roles', name: 'roles', orderable: false, searchable: false },
            { data: 'status_badge', name: 'is_active' },
            { data: 'action', name: 'action', orderable: false, searchable: false }
        ],
        order: [[1, 'asc']],
        pageLength: 25,
        responsive: true,
        dom: 'Bfrtip',
        buttons: [
            {
                extend: 'excel',
                text: '<i class="fas fa-file-excel"></i> Excel',
                className: 'btn btn-success btn-sm'
            },
            {
                extend: 'pdf',
                text: '<i class="fas fa-file-pdf"></i> PDF',
                className: 'btn btn-danger btn-sm'
            }
        ],
        language: {
            url: '//cdn.datatables.net/plug-ins/1.13.7/i18n/ar.json'
        }
    });

    // Global search
    $('#globalSearch').on('keyup', function() {
        table.search(this.value).draw();
    });

    // Filter handlers
    $('#tenantFilter, #branchFilter, #roleFilter, #statusFilter').on('change', function() {
        table.draw();
    });

    // Export buttons
    $('#exportExcel').on('click', function() {
        table.button('.buttons-excel').trigger();
    });

    $('#exportPdf').on('click', function() {
        table.button('.buttons-pdf').trigger();
    });

    // Add User Form
    $('#addUserForm').on('submit', function(e) {
        e.preventDefault();
        
        // Clear previous errors
        $('.text-red-500').addClass('hidden');
        
        $.ajax({
            url: '<?php echo e(route("users.store")); ?>',
            method: 'POST',
            data: $(this).serialize(),
            success: function(response) {
                if (response.success) {
                    closeModal('addUserModal');
                    $('#addUserForm')[0].reset();
                    table.ajax.reload();
                    showNotification('success', response.message);
                }
            },
            error: function(xhr) {
                if (xhr.status === 422) {
                    var errors = xhr.responseJSON.errors;
                    $.each(errors, function(key, value) {
                        $('#add_' + key + '_error').text(value[0]).removeClass('hidden');
                    });
                } else {
                    showNotification('error', 'حدث خطأ أثناء إضافة المستخدم');
                }
            }
        });
    });

    // Edit User Form
    $('#editUserForm').on('submit', function(e) {
        e.preventDefault();
        
        var userId = $('#edit_user_id').val();
        
        // Clear previous errors
        $('.text-red-500').addClass('hidden');
        
        $.ajax({
            url: '/admin/users/' + userId,
            method: 'PUT',
            data: $(this).serialize(),
            success: function(response) {
                if (response.success) {
                    closeModal('editUserModal');
                    table.ajax.reload();
                    showNotification('success', response.message);
                }
            },
            error: function(xhr) {
                if (xhr.status === 422) {
                    var errors = xhr.responseJSON.errors;
                    $.each(errors, function(key, value) {
                        $('#edit_' + key + '_error').text(value[0]).removeClass('hidden');
                    });
                } else {
                    showNotification('error', 'حدث خطأ أثناء تحديث المستخدم');
                }
            }
        });
    });

    // Assign Roles Form
    $('#assignRolesForm').on('submit', function(e) {
        e.preventDefault();
        
        var userId = $('#assign_user_id').val();
        
        $.ajax({
            url: '/admin/users/' + userId + '/roles',
            method: 'POST',
            data: $(this).serialize(),
            success: function(response) {
                if (response.success) {
                    closeModal('assignRolesModal');
                    table.ajax.reload();
                    showNotification('success', response.message);
                }
            },
            error: function(xhr) {
                showNotification('error', 'حدث خطأ أثناء تعيين الأدوار');
            }
        });
    });
});

// Modal functions
function openModal(modalId) {
    document.getElementById(modalId).classList.remove('hidden');
    document.body.style.overflow = 'hidden';
}

function closeModal(modalId) {
    document.getElementById(modalId).classList.add('hidden');
    document.body.style.overflow = 'auto';
    
    // Clear form data and errors
    if (modalId === 'addUserModal') {
        $('#addUserForm')[0].reset();
    } else if (modalId === 'editUserModal') {
        $('#editUserForm')[0].reset();
    } else if (modalId === 'assignRolesModal') {
        $('#assignRolesForm')[0].reset();
    }
    
    $('.text-red-500').addClass('hidden');
}

// User action functions
function editUser(userId) {
    $.ajax({
        url: '/admin/users/' + userId,
        method: 'GET',
        success: function(response) {
            // Populate edit form
            $('#edit_user_id').val(response.id);
            $('#edit_name').val(response.name);
            $('#edit_email').val(response.email);
            $('#edit_tenant_id').val(response.tenant_id);
            $('#edit_branch_id').val(response.branch_id);
            $('#edit_employee_id').val(response.employee_id);
            $('#edit_position').val(response.position);
            $('#edit_department').val(response.department);
            $('#edit_hourly_rate').val(response.hourly_rate);
            $('#edit_salary').val(response.salary);
            $('#edit_base_salary').val(response.base_salary);
            
            // Set roles
            $('#edit_roles_container input[type="checkbox"]').prop('checked', false);
            if (response.roles) {
                response.roles.forEach(function(role) {
                    $('#edit_roles_container input[value="' + role.name + '"]').prop('checked', true);
                });
            }
            
            openModal('editUserModal');
        },
        error: function() {
            showNotification('error', 'حدث خطأ أثناء تحميل بيانات المستخدم');
        }
    });
}



function assignRoles(userId) {
    $.ajax({
        url: '/admin/users/' + userId + '/roles',
        method: 'GET',
        success: function(response) {
            $('#assign_user_id').val(response.user.id);
            $('#assign_user_name').text(response.user.name);
            
            // Clear all checkboxes
            $('#assign_roles_container input[type="checkbox"]').prop('checked', false);
            
            // Check user's current roles
            if (response.userRoles) {
                response.userRoles.forEach(function(role) {
                    $('#assign_roles_container input[value="' + role + '"]').prop('checked', true);
                });
            }
            
            openModal('assignRolesModal');
        },
        error: function() {
            showNotification('error', 'حدث خطأ أثناء تحميل أدوار المستخدم');
        }
    });
}

function changeStatus(userId, action) {
    var message = action === 'activate' ? 'تفعيل' : 'إلغاء تفعيل';
    var title = action === 'activate' ? 'تفعيل المستخدم' : 'إلغاء تفعيل المستخدم';
    
    Swal.fire({
        title: title,
        text: 'هل أنت متأكد من ' + message + ' هذا المستخدم؟',
        icon: 'warning',
        showCancelButton: true,
        confirmButtonColor: action === 'activate' ? '#10b981' : '#ef4444',
        cancelButtonColor: '#6b7280',
        confirmButtonText: 'نعم، ' + message,
        cancelButtonText: 'إلغاء',
        reverseButtons: true
    }).then((result) => {
        if (result.isConfirmed) {
            $.ajax({
                url: '/admin/users/' + userId + '/' + action,
                method: 'POST',
                data: {
                    _token: $('meta[name="csrf-token"]').attr('content')
                },
                success: function(response) {
                    if (response.success) {
                        $('#usersTable').DataTable().ajax.reload();
                        showNotification('success', response.message);
                    }
                },
                error: function() {
                    showNotification('error', 'حدث خطأ أثناء تغيير حالة المستخدم');
                }
            });
        }
    });
}

function resetPassword(userId) {
    Swal.fire({
        title: 'إعادة تعيين كلمة المرور',
        text: 'هل أنت متأكد من إعادة تعيين كلمة مرور هذا المستخدم؟',
        icon: 'warning',
        showCancelButton: true,
        confirmButtonColor: '#f59e0b',
        cancelButtonColor: '#6b7280',
        confirmButtonText: 'نعم، إعادة تعيين',
        cancelButtonText: 'إلغاء',
        reverseButtons: true
    }).then((result) => {
        if (result.isConfirmed) {
            $.ajax({
                url: '/admin/users/' + userId + '/reset-password',
                method: 'POST',
                data: {
                    _token: $('meta[name="csrf-token"]').attr('content')
                },
                success: function(response) {
                    if (response.success) {
                        showNotification('success', response.message);
                    }
                },
                error: function() {
                    showNotification('error', 'حدث خطأ أثناء إعادة تعيين كلمة المرور');
                }
            });
        }
    });
}

function deleteUser(userId) {
    Swal.fire({
        title: 'حذف المستخدم',
        text: 'هل أنت متأكد من حذف هذا المستخدم؟ هذا الإجراء لا يمكن التراجع عنه.',
        icon: 'error',
        showCancelButton: true,
        confirmButtonColor: '#ef4444',
        cancelButtonColor: '#6b7280',
        confirmButtonText: 'نعم، احذف',
        cancelButtonText: 'إلغاء',
        reverseButtons: true
    }).then((result) => {
        if (result.isConfirmed) {
            $.ajax({
                url: '/admin/users/' + userId,
                method: 'DELETE',
                data: {
                    _token: $('meta[name="csrf-token"]').attr('content')
                },
                success: function(response) {
                    if (response.success) {
                        $('#usersTable').DataTable().ajax.reload();
                        showNotification('success', response.message);
                    }
                },
                error: function() {
                    showNotification('error', 'حدث خطأ أثناء حذف المستخدم');
                }
            });
        }
    });
}

// Notification function
function showNotification(type, message) {
    if (type === 'success') {
        Swal.fire({
            icon: 'success',
            title: 'نجح!',
            text: message,
            timer: 3000,
            showConfirmButton: false,
            toast: true,
            position: 'top-end'
        });
    } else {
        Swal.fire({
            icon: 'error',
            title: 'خطأ!',
            text: message,
            timer: 5000,
            showConfirmButton: true,
            toast: true,
            position: 'top-end'
        });
    }
}

// Close modal when clicking outside
$(document).on('click', function(e) {
    if ($(e.target).hasClass('fixed') && $(e.target).hasClass('inset-0')) {
        var openModal = $('.fixed.inset-0:not(.hidden)');
        if (openModal.length > 0) {
            var modalId = openModal.attr('id');
            closeModal(modalId);
        }
    }
});

// Close modal with Escape key
$(document).on('keydown', function(e) {
    if (e.key === 'Escape') {
        var openModal = $('.fixed.inset-0:not(.hidden)');
        if (openModal.length > 0) {
            var modalId = openModal.attr('id');
            closeModal(modalId);
        }
    }
});
</script>
<?php $__env->stopPush(); ?>
<?php echo $__env->make('layouts.master', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH D:\my dehive work\resturant-pos - Copy\epis - Copy\modules\Auth\Providers/../resources/views/users/index.blade.php ENDPATH**/ ?>