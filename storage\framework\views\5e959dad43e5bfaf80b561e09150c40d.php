<?php $__env->startSection('title', 'إدارة الحجوزات'); ?>

<?php $__env->startPush('styles'); ?>
<meta name="csrf-token" content="<?php echo e(csrf_token()); ?>">
<!-- DataTables CSS -->
<link rel="stylesheet" href="https://cdn.datatables.net/1.13.7/css/jquery.dataTables.min.css">
<link rel="stylesheet" href="https://cdn.datatables.net/buttons/2.4.2/css/buttons.dataTables.min.css">
<link rel="stylesheet" href="https://cdn.datatables.net/responsive/2.5.0/css/responsive.dataTables.min.css">
<!-- Select2 CSS -->
<link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet" />
<style>
    /* Custom DataTables Tailwind Styling */
    .dataTables_wrapper {
        font-family: inherit;
    }
    
    .dataTables_wrapper .dataTables_length,
    .dataTables_wrapper .dataTables_filter,
    .dataTables_wrapper .dataTables_info,
    .dataTables_wrapper .dataTables_processing,
    .dataTables_wrapper .dataTables_paginate {
        color: #374151;
        font-size: 0.875rem;
        padding: 0.5rem 0.75rem;
    }
    .dataTables_processing{
        margin: auto
    }
    .dataTables_wrapper .dataTables_length select,
    .dataTables_wrapper .dataTables_filter input {
        border: 1px solid #d1d5db;
        border-radius: 0.375rem;
        padding: 0.375rem 0.75rem;
        font-size: 0.875rem;
    }
    
    .dataTables_wrapper .dataTables_filter input:focus {
        outline: none;
        border-color: #3b82f6;
        box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
    }
    
    .dataTables_wrapper .dataTables_paginate .paginate_button {
        padding: 0.5rem 0.75rem !important;
        margin: 0 0.125rem !important;
        border-radius: 0.375rem !important;
        border: 1px solid #d1d5db !important;
        background: white !important;
        color: #374151 !important;
        text-decoration: none !important;
    }
    
    .dataTables_wrapper .dataTables_paginate .paginate_button:hover {
        background: #f3f4f6 !important;
        border-color: #9ca3af !important;
        color: #374151 !important;
    }
    
    .dataTables_wrapper .dataTables_paginate .paginate_button.current {
        background: #3b82f6 !important;
        border-color: #3b82f6 !important;
        color: white !important;
    }
    
    .dataTables_wrapper .dataTables_paginate .paginate_button.disabled {
        background: #f9fafb !important;
        color: #9ca3af !important;
        border-color: #e5e7eb !important;
    }
    
    table.dataTable {
        border-collapse: separate !important;
        border-spacing: 0 !important;
    }
    
    table.dataTable thead th {
        background-color: #f9fafb;
        border-bottom: 1px solid #e5e7eb;
        font-weight: 600;
        color: #374151;
        padding: 0.75rem;
    }
    
    table.dataTable tbody td {
        padding: 0.75rem;
        border-bottom: 1px solid #f3f4f6;
    }
    
    table.dataTable tbody tr:hover {
        background-color: #f9fafb;
    }
    
    .select2-container--default .select2-selection--single {
        height: 42px;
        border: 1px solid #d1d5db;
        border-radius: 0.5rem;
        padding: 0.5rem;
    }
    
    .select2-container--default .select2-selection--single .select2-selection__rendered {
        line-height: 26px;
        padding-left: 0;
    }
    
    .select2-container--default .select2-selection--single .select2-selection__arrow {
        height: 40px;
    }
    
    .modal-backdrop {
        background-color: rgba(0, 0, 0, 0.5);
    }
    
    .animate-fadeIn {
        animation: fadeIn 0.3s ease-in-out;
    }
    
    @keyframes fadeIn {
        from { opacity: 0; transform: scale(0.95); }
        to { opacity: 1; transform: scale(1); }
    }
    
    .animate-slideDown {
        animation: slideDown 0.3s ease-in-out;
    }
    
    @keyframes slideDown {
        from { opacity: 0; transform: translateY(-10px); }
        to { opacity: 1; transform: translateY(0); }
    }
</style>
<?php $__env->stopPush(); ?>

<?php $__env->startSection('content'); ?>
<div class="container-fluid" dir="rtl">
    <!-- Page Header -->
    <div class="bg-white rounded-lg shadow-sm p-6 mb-6">
        <div class="flex flex-col md:flex-row md:items-center md:justify-between">
            <div>
                <h1 class="text-2xl font-bold text-gray-900 mb-2">إدارة الحجوزات</h1>
                <nav class="flex" aria-label="Breadcrumb">
                    <ol class="inline-flex items-center space-x-1 md:space-x-3">
                        <li class="inline-flex items-center">
                            <a href="<?php echo e(route('dashboard')); ?>" class="inline-flex items-center text-sm font-medium text-gray-700 hover:text-blue-600">
                                <i class="fas fa-home ml-2"></i>
                                الرئيسية
                            </a>
                        </li>
                        <li>
                            <div class="flex items-center">
                                <i class="fas fa-chevron-left text-gray-400 mx-2"></i>
                                <span class="ml-1 text-sm font-medium text-gray-500 md:ml-2">الحجوزات</span>
                            </div>
                        </li>
                    </ol>
                </nav>
            </div>
            <div class="mt-4 md:mt-0">
                <button type="button" id="addReservationBtn" class="inline-flex items-center px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white text-sm font-medium rounded-lg shadow-sm transition-colors duration-200">
                    <i class="fas fa-plus ml-2"></i>
                    إضافة حجز جديد
                </button>
            </div>
        </div>
    </div>

    <!-- Filters Section -->
    <div class="bg-white rounded-lg shadow-sm p-6 mb-6">
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4">
            <div>
                <label for="filter_status" class="block text-sm font-medium text-gray-700 mb-2">حالة الحجز</label>
                <select id="filter_status" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                    <option value="">جميع الحالات</option>
                    <option value="pending">في الانتظار</option>
                    <option value="confirmed">مؤكد</option>
                    <option value="seated">جالس</option>
                    <option value="completed">مكتمل</option>
                    <option value="cancelled">ملغي</option>
                    <option value="no_show">لم يحضر</option>
                </select>
            </div>
            <div>
                <label for="filter_area" class="block text-sm font-medium text-gray-700 mb-2">المنطقة</label>
                <select id="filter_area" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                    <option value="">جميع المناطق</option>
                </select>
            </div>
            <div>
                <label for="filter_table" class="block text-sm font-medium text-gray-700 mb-2">الطاولة</label>
                <select id="filter_table" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                    <option value="">جميع الطاولات</option>
                </select>
            </div>
            <div>
                <label for="filter_date_from" class="block text-sm font-medium text-gray-700 mb-2">من تاريخ</label>
                <input type="date" id="filter_date_from" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
            </div>
            <div class="flex items-end">
                <button type="button" id="resetFilters" class="w-full px-4 py-2 bg-gray-500 hover:bg-gray-600 text-white text-sm font-medium rounded-lg transition-colors duration-200">
                    <i class="fas fa-undo ml-2"></i>
                    إعادة تعيين
                </button>
            </div>
        </div>
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mt-4">
            <div>
                <label for="filter_date_to" class="block text-sm font-medium text-gray-700 mb-2">إلى تاريخ</label>
                <input type="date" id="filter_date_to" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
            </div>
            <div>
                <label for="filter_customer" class="block text-sm font-medium text-gray-700 mb-2">بحث العميل</label>
                <input type="text" id="filter_customer" placeholder="اسم أو رقم هاتف العميل" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
            </div>
        </div>
    </div>

    <!-- DataTable Section -->
    <div class="bg-white rounded-lg shadow-sm overflow-hidden">
        <div class="p-6">
            <div class="overflow-x-auto">
                <table id="reservationsTable" class="w-full text-sm text-right text-gray-500">
                    <thead class="text-xs text-gray-700 uppercase bg-gray-50">
                        <tr>
                            <th scope="col" class="px-6 py-3">#</th>
                            <th scope="col" class="px-6 py-3">اسم العميل</th>
                            <th scope="col" class="px-6 py-3">رقم الهاتف</th>
                            <th scope="col" class="px-6 py-3">المنطقة</th>
                            <th scope="col" class="px-6 py-3">الطاولة</th>
                            <th scope="col" class="px-6 py-3">تاريخ الحجز</th>
                            <th scope="col" class="px-6 py-3">عدد الأشخاص</th>
                            <th scope="col" class="px-6 py-3">الحالة</th>
                            <th scope="col" class="px-6 py-3">الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody></tbody>
                </table>
            </div>
        </div>
    </div>
</div>

<!-- Add/Edit Reservation Modal -->
<div id="reservationModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50 hidden">
    <div class="relative top-20 mx-auto p-5 border w-11/12 md:w-3/4 lg:w-1/2 shadow-lg rounded-md bg-white animate-fadeIn">
        <div class="mt-3">
            <!-- Modal Header -->
            <div class="flex items-center justify-between p-4 border-b">
                <h3 class="text-lg font-semibold text-gray-900" id="modalTitle">إضافة حجز جديد</h3>
                <button type="button" class="text-gray-400 bg-transparent hover:bg-gray-200 hover:text-gray-900 rounded-lg text-sm p-1.5 ml-auto inline-flex items-center" id="closeModal">
                    <i class="fas fa-times w-5 h-5"></i>
                </button>
            </div>
            
            <!-- Modal Body -->
            <div class="p-6">
                <form id="reservationForm" class="space-y-6">
                    <input type="hidden" id="reservationId" name="id">
                    
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <!-- Customer Selection -->
                        <div>
                            <label for="customer_id" class="block text-sm font-medium text-gray-700 mb-2">العميل *</label>
                            <select id="customer_id" name="customer_id" required class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                                <option value="">اختر العميل</option>
                            </select>
                            <div class="text-red-500 text-sm mt-1 hidden" id="customer_id_error"></div>
                        </div>
                        
                        <!-- Area Selection -->
                        <div>
                            <label for="area_id" class="block text-sm font-medium text-gray-700 mb-2">المنطقة *</label>
                            <select id="area_id" name="area_id" required class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                                <option value="">اختر المنطقة</option>
                            </select>
                            <div class="text-red-500 text-sm mt-1 hidden" id="area_id_error"></div>
                        </div>
                    </div>
                    
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <!-- Table Selection -->
                        <div>
                            <label for="table_id" class="block text-sm font-medium text-gray-700 mb-2">الطاولة *</label>
                            <select id="table_id" name="table_id" required class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                                <option value="">اختر الطاولة</option>
                            </select>
                            <div class="text-red-500 text-sm mt-1 hidden" id="table_id_error"></div>
                        </div>
                        
                        <!-- Party Size -->
                        <div>
                            <label for="party_size" class="block text-sm font-medium text-gray-700 mb-2">عدد الأشخاص *</label>
                            <input type="number" id="party_size" name="party_size" min="1" max="20" required class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500" placeholder="عدد الأشخاص">
                            <div class="text-red-500 text-sm mt-1 hidden" id="party_size_error"></div>
                        </div>
                    </div>
                    
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <!-- Reservation Date -->
                        <div>
                            <label for="reservation_date" class="block text-sm font-medium text-gray-700 mb-2">تاريخ الحجز *</label>
                            <input type="date" id="reservation_date" name="reservation_date" required class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                            <div class="text-red-500 text-sm mt-1 hidden" id="reservation_date_error"></div>
                        </div>
                        
                        <!-- Reservation Time -->
                        <div>
                            <label for="reservation_time" class="block text-sm font-medium text-gray-700 mb-2">وقت الحجز *</label>
                            <input type="time" id="reservation_time" name="reservation_time" required class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                            <div class="text-red-500 text-sm mt-1 hidden" id="reservation_time_error"></div>
                        </div>
                    </div>
                    
                    <!-- Special Requests -->
                    <div>
                        <label for="special_requests" class="block text-sm font-medium text-gray-700 mb-2">طلبات خاصة</label>
                        <textarea id="special_requests" name="special_requests" rows="3" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500" placeholder="أي طلبات خاصة للحجز"></textarea>
                        <div class="text-red-500 text-sm mt-1 hidden" id="special_requests_error"></div>
                    </div>
                    
                    <!-- Status (for edit mode) -->
                    <div id="statusField" class="hidden">
                        <label for="status" class="block text-sm font-medium text-gray-700 mb-2">حالة الحجز</label>
                        <select id="status" name="status" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                            <option value="pending">في الانتظار</option>
                            <option value="confirmed">مؤكد</option>
                            <option value="seated">جالس</option>
                            <option value="completed">مكتمل</option>
                            <option value="cancelled">ملغي</option>
                            <option value="no_show">لم يحضر</option>
                        </select>
                        <div class="text-red-500 text-sm mt-1 hidden" id="status_error"></div>
                    </div>
                </form>
            </div>
            
            <!-- Modal Footer -->
            <div class="flex items-center justify-end p-6 border-t border-gray-200 space-x-2">
                <button type="button" id="cancelBtn" class="px-4 py-2 bg-gray-500 hover:bg-gray-600 text-white text-sm font-medium rounded-lg transition-colors duration-200">
                    إلغاء
                </button>
                <button type="button" id="saveBtn" class="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white text-sm font-medium rounded-lg transition-colors duration-200">
                    <i class="fas fa-save ml-2"></i>
                    حفظ
                </button>
            </div>
        </div>
    </div>
</div>

<!-- View Reservation Modal -->
<div id="viewReservationModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50 hidden">
    <div class="relative top-20 mx-auto p-5 border w-11/12 md:w-3/4 lg:w-1/2 shadow-lg rounded-md bg-white animate-fadeIn">
        <div class="mt-3">
            <!-- Modal Header -->
            <div class="flex items-center justify-between p-4 border-b">
                <h3 class="text-lg font-semibold text-gray-900">تفاصيل الحجز</h3>
                <button type="button" class="text-gray-400 bg-transparent hover:bg-gray-200 hover:text-gray-900 rounded-lg text-sm p-1.5 ml-auto inline-flex items-center" id="closeViewModal">
                    <i class="fas fa-times w-5 h-5"></i>
                </button>
            </div>
            
            <!-- Modal Body -->
            <div class="p-6">
                <div id="reservationDetails" class="space-y-4">
                    <!-- Details will be populated here -->
                </div>
            </div>
            
            <!-- Modal Footer -->
            <div class="flex items-center justify-end p-6 border-t border-gray-200">
                <button type="button" id="closeViewBtn" class="px-4 py-2 bg-gray-500 hover:bg-gray-600 text-white text-sm font-medium rounded-lg transition-colors duration-200">
                    إغلاق
                </button>
            </div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('scripts'); ?>
<!-- jQuery -->
<script src="https://code.jquery.com/jquery-3.7.1.min.js"></script>
<!-- DataTables JS -->
<script src="https://cdn.datatables.net/1.13.7/js/jquery.dataTables.min.js"></script>
<script src="https://cdn.datatables.net/buttons/2.4.2/js/dataTables.buttons.min.js"></script>
<script src="https://cdn.datatables.net/responsive/2.5.0/js/dataTables.responsive.min.js"></script>
<!-- Select2 JS -->
<script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>

<script>
$(document).ready(function() {
    // CSRF Token Setup
    $.ajaxSetup({
        headers: {
            'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
        }
    });

    // Initialize DataTable
    let table = $('#reservationsTable').DataTable({
        processing: true,
        serverSide: true,
        ajax: {
            url: '<?php echo e(route("reservations.data")); ?>',
            data: function(d) {
                d.status = $('#filter_status').val();
                d.area_id = $('#filter_area').val();
                d.table_id = $('#filter_table').val();
                d.date_from = $('#filter_date_from').val();
                d.date_to = $('#filter_date_to').val();
                d.customer_search = $('#filter_customer').val();
            }
        },
        columns: [
            { data: 'DT_RowIndex', name: 'DT_RowIndex', orderable: false, searchable: false },
            { data: 'customer_name', name: 'customer_name' },
            { data: 'customer_phone', name: 'customer_phone' },
            { data: 'area_name', name: 'area_name' },
            { data: 'table_name', name: 'table_name' },
            { data: 'reservation_datetime', name: 'reservation_datetime' },
            { data: 'party_size', name: 'party_size' },
            { data: 'status', name: 'status' },
            { data: 'action', name: 'action', orderable: false, searchable: false }
        ],
        order: [[5, 'desc']],
        pageLength: 25,
        responsive: true,
        language: {
            "sProcessing": "جارٍ التحميل...",
            "sLengthMenu": "أظهر _MENU_ ",
            "sZeroRecords": "لم يعثر على أية سجلات",
            "sInfo": "إظهار _START_ إلى _END_ من أصل _TOTAL_ مدخل",
            "sInfoEmpty": "يعرض 0 إلى 0 من أصل 0 سجل",
            "sInfoFiltered": "(منتقاة من مجموع _MAX_ مُدخل)",
            "sInfoPostFix": "",
            "sSearch": "بحث",
            "sUrl": "",
            "oPaginate": {
                "sFirst": "الأول",
                "sPrevious": "السابق",
                "sNext": "التالي",
                "sLast": "الأخير"
            }
        },
        drawCallback: function() {
            // Re-initialize tooltips or other UI elements if needed
        }
    });

    // Filter event handlers
    $('#filter_status, #filter_area, #filter_table, #filter_date_from, #filter_date_to').change(function() {
        table.draw();
    });

    $('#filter_customer').on('keyup', function() {
        table.draw();
    });

    // Reset filters
    $('#resetFilters').click(function() {
        $('#filter_status, #filter_area, #filter_table').val('').trigger('change');
        $('#filter_date_from, #filter_date_to, #filter_customer').val('');
        table.draw();
    });

    // Load dropdown data
    loadDropdownData();

    // Modal event handlers
    $('#addReservationBtn').click(function() {
        openReservationModal();
    });

    $('#closeModal, #cancelBtn').click(function() {
        closeReservationModal();
    });

    $('#closeViewModal, #closeViewBtn').click(function() {
        closeViewModal();
    });

    $('#saveBtn').click(function() {
        saveReservation();
    });

    // Area change handler to load tables
    $('#area_id').change(function() {
        loadTablesByArea($(this).val());
    });

    // Functions
    function loadDropdownData() {
        // Load customers
        $.get('<?php echo e(route("customers.list")); ?>', function(data) {
            let customerSelect = $('#customer_id');
            customerSelect.empty().append('<option value="">اختر العميل</option>');
            data.forEach(function(customer) {
                customerSelect.append(`<option value="${customer.id}">${customer.name} - ${customer.phone}</option>`);
            });
        });

        // Load areas
        $.get('<?php echo e(route("areas.list")); ?>', function(data) {
            let areaSelect = $('#area_id, #filter_area');
            areaSelect.empty().append('<option value="">اختر المنطقة</option>');
            data.forEach(function(area) {
                areaSelect.append(`<option value="${area.id}">${area.name}</option>`);
            });
        });

        // Load all tables for filter
        $.get('<?php echo e(route("tables.list")); ?>', function(data) {
            let tableSelect = $('#filter_table');
            tableSelect.empty().append('<option value="">جميع الطاولات</option>');
            data.forEach(function(table) {
                tableSelect.append(`<option value="${table.id}">${table.name}</option>`);
            });
        });
    }

    function loadTablesByArea(areaId) {
        let tableSelect = $('#table_id');
        tableSelect.empty().append('<option value="">اختر الطاولة</option>');
        
        if (areaId) {
            $.get(`<?php echo e(url('/areas')); ?>/${areaId}/tables`, function(data) {
                data.forEach(function(table) {
                    tableSelect.append(`<option value="${table.id}">${table.name} (${table.seating_capacity} أشخاص)</option>`);
                });
            });
        }
    }

    function openReservationModal(id = null) {
        if (id) {
            // Edit mode
            $('#modalTitle').text('تعديل الحجز');
            $('#statusField').removeClass('hidden');
            loadReservationData(id);
        } else {
            // Add mode
            $('#modalTitle').text('إضافة حجز جديد');
            $('#statusField').addClass('hidden');
            $('#reservationForm')[0].reset();
            $('#reservationId').val('');
            clearErrors();
        }
        $('#reservationModal').removeClass('hidden');
    }

    function closeReservationModal() {
        $('#reservationModal').addClass('hidden');
        $('#reservationForm')[0].reset();
        clearErrors();
    }

    function closeViewModal() {
        $('#viewReservationModal').addClass('hidden');
    }

    function loadReservationData(id) {
        $.get(`<?php echo e(url('/reservations')); ?>/${id}`, function(data) {
            $('#reservationId').val(data.id);
            $('#customer_id').val(data.customer_id);
            $('#area_id').val(data.area_id);
            $('#party_size').val(data.party_size);
            $('#special_requests').val(data.special_requests);
            $('#status').val(data.status);
            
            // Parse datetime
            let datetime = new Date(data.reservation_datetime);
            $('#reservation_date').val(datetime.toISOString().split('T')[0]);
            $('#reservation_time').val(datetime.toTimeString().split(' ')[0].substring(0, 5));
            
            // Load tables for the selected area
            loadTablesByArea(data.area_id);
            setTimeout(() => {
                $('#table_id').val(data.table_id);
            }, 500);
        }).fail(function() {
            alert('حدث خطأ في تحميل بيانات الحجز');
        });
    }

    function saveReservation() {
        let formData = {
            customer_id: $('#customer_id').val(),
            area_id: $('#area_id').val(),
            table_id: $('#table_id').val(),
            party_size: $('#party_size').val(),
            reservation_datetime: $('#reservation_date').val() + ' ' + $('#reservation_time').val(),
            special_requests: $('#special_requests').val()
        };

        let id = $('#reservationId').val();
        if (id) {
            formData.status = $('#status').val();
        }

        let url = id ? `<?php echo e(url('/reservations')); ?>/${id}` : '<?php echo e(url('/reservations')); ?>';
        let method = id ? 'PUT' : 'POST';

        $.ajax({
            url: url,
            method: method,
            data: formData,
            success: function(response) {
                closeReservationModal();
                table.draw();
                alert(id ? 'تم تحديث الحجز بنجاح' : 'تم إضافة الحجز بنجاح');
            },
            error: function(xhr) {
                if (xhr.status === 422) {
                    displayErrors(xhr.responseJSON.errors);
                } else {
                    alert('حدث خطأ في حفظ الحجز');
                }
            }
        });
    }

    function displayErrors(errors) {
        clearErrors();
        for (let field in errors) {
            $(`#${field}_error`).text(errors[field][0]).removeClass('hidden');
        }
    }

    function clearErrors() {
        $('.text-red-500').addClass('hidden').text('');
    }

    // Global functions for DataTable actions
    window.showReservation = function(id) {
        $.get(`<?php echo e(url('/reservations')); ?>/${id}`, function(data) {
            let details = `
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div><strong>اسم العميل:</strong> ${data.customer ? data.customer.name : '-'}</div>
                    <div><strong>رقم الهاتف:</strong> ${data.customer ? data.customer.phone : '-'}</div>
                    <div><strong>المنطقة:</strong> ${data.area ? data.area.name : '-'}</div>
                    <div><strong>الطاولة:</strong> ${data.table ? data.table.name : '-'}</div>
                    <div><strong>تاريخ الحجز:</strong> ${new Date(data.reservation_datetime).toLocaleString('ar-EG')}</div>
                    <div><strong>عدد الأشخاص:</strong> ${data.party_size}</div>
                    <div><strong>الحالة:</strong> ${getStatusLabel(data.status)}</div>
                    <div><strong>تاريخ الإنشاء:</strong> ${new Date(data.created_at).toLocaleString('ar-EG')}</div>
                </div>
                ${data.special_requests ? `<div class="mt-4"><strong>طلبات خاصة:</strong><br>${data.special_requests}</div>` : ''}
            `;
            $('#reservationDetails').html(details);
            $('#viewReservationModal').removeClass('hidden');
        });
    };

    window.editReservation = function(id) {
        openReservationModal(id);
    };

    window.confirmReservation = function(id) {
        if (confirm('هل أنت متأكد من تأكيد هذا الحجز؟')) {
            $.post(`<?php echo e(url('/reservations')); ?>/${id}/confirm`, function() {
                table.draw();
                alert('تم تأكيد الحجز بنجاح');
            }).fail(function() {
                alert('حدث خطأ في تأكيد الحجز');
            });
        }
    };

    window.deleteReservation = function(id) {
        if (confirm('هل أنت متأكد من حذف هذا الحجز؟')) {
            $.ajax({
                url: `<?php echo e(url('/reservations')); ?>/${id}`,
                method: 'DELETE',
                success: function() {
                    table.draw();
                    alert('تم حذف الحجز بنجاح');
                },
                error: function() {
                    alert('حدث خطأ في حذف الحجز');
                }
            });
        }
    };

    function getStatusLabel(status) {
        const labels = {
            'pending': 'في الانتظار',
            'confirmed': 'مؤكد',
            'seated': 'جالس',
            'completed': 'مكتمل',
            'cancelled': 'ملغي',
            'no_show': 'لم يحضر'
        };
        return labels[status] || status;
    }
});
</script>
<?php $__env->stopPush(); ?>
<?php echo $__env->make('layouts.master', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH D:\my dehive work\resturant-pos - Copy\epis - Copy\modules\Reservation\Providers/../resources/views/reservations.blade.php ENDPATH**/ ?>