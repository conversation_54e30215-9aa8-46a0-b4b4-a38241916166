<?php $__env->startSection('title', 'عرض المستخدم'); ?>

<?php $__env->startSection('content'); ?>
<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
    <!-- Page Header -->
    <div class="mb-6">
        <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between">
            <h1 class="text-2xl font-semibold text-gray-900 mb-4 sm:mb-0">عرض المستخدم</h1>
            <nav class="flex" aria-label="Breadcrumb">
                <ol class="inline-flex items-center space-x-1 md:space-x-3 space-x-reverse">
                    <li class="inline-flex items-center">
                        <a href="<?php echo e(route('dashboard')); ?>" class="inline-flex items-center text-sm font-medium text-gray-700 hover:text-blue-600">
                            الرئيسية
                        </a>
                    </li>
                    <li>
                        <div class="flex items-center">
                            <svg class="w-3 h-3 text-gray-400 mx-1 rotate-180" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 6 10">
                                <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="m1 9 4-4-4-4"/>
                            </svg>
                            <a href="<?php echo e(route('users.index')); ?>" class="ml-1 text-sm font-medium text-gray-700 hover:text-blue-600 md:ml-2">المستخدمين</a>
                        </div>
                    </li>
                    <li aria-current="page">
                        <div class="flex items-center">
                            <svg class="w-3 h-3 text-gray-400 mx-1 rotate-180" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 6 10">
                                <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="m1 9 4-4-4-4"/>
                            </svg>
                            <span class="ml-1 text-sm font-medium text-gray-500 md:ml-2">عرض المستخدم</span>
                        </div>
                    </li>
                </ol>
            </nav>
        </div>
    </div>

    <!-- User Details Card -->
    <div class="bg-white shadow-sm rounded-lg overflow-hidden">
        <!-- Card Header -->
        <div class="px-6 py-4 border-b border-gray-200">
            <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between">
                <h2 class="text-lg font-medium text-gray-900 mb-4 sm:mb-0">تفاصيل المستخدم</h2>
                <div class="flex space-x-2 space-x-reverse">
                    <a href="<?php echo e(route('users.edit', $user->id)); ?>" class="inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                        <svg class="w-4 h-4 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                        </svg>
                        تعديل
                    </a>
                    <a href="<?php echo e(route('users.index')); ?>" class="inline-flex items-center px-3 py-2 border border-gray-300 text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                        <svg class="w-4 h-4 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
                        </svg>
                        العودة
                    </a>
                </div>
            </div>
        </div>

        <!-- Card Body -->
        <div class="px-6 py-6">
            <div class="grid grid-cols-1 lg:grid-cols-12 gap-6">
                <!-- User Avatar -->
                <div class="lg:col-span-3">
                    <div class="text-center">
                        <div class="mb-4">
                            <?php if($user->avatar): ?>
                                <img src="<?php echo e(asset('storage/' . $user->avatar)); ?>" alt="<?php echo e($user->name); ?>" 
                                     class="w-30 h-30 rounded-full mx-auto object-cover border-4 border-gray-200">
                            <?php else: ?>
                                <div class="w-30 h-30 rounded-full mx-auto bg-blue-500 flex items-center justify-center text-white text-3xl font-semibold">
                                    <?php echo e(strtoupper(substr($user->name, 0, 1))); ?>

                                </div>
                            <?php endif; ?>
                        </div>
                        <h3 class="text-lg font-medium text-gray-900 mb-1"><?php echo e($user->name); ?></h3>
                        <p class="text-sm text-gray-500 mb-3"><?php echo e($user->email); ?></p>
                        <div>
                            <?php if($user->is_active): ?>
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                    نشط
                                </span>
                            <?php else: ?>
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                                    غير نشط
                                </span>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>

                <!-- User Information -->
                <div class="lg:col-span-9">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">الاسم الكامل:</label>
                            <p class="text-sm text-gray-900"><?php echo e($user->name); ?></p>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">البريد الإلكتروني:</label>
                            <p class="text-sm text-gray-900"><?php echo e($user->email); ?></p>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">رقم الهاتف:</label>
                            <p class="text-sm text-gray-900"><?php echo e($user->phone ?? 'غير محدد'); ?></p>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">المنصب:</label>
                            <p class="text-sm text-gray-900"><?php echo e($user->position ?? 'غير محدد'); ?></p>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">القسم:</label>
                            <p class="text-sm text-gray-900"><?php echo e($user->department ?? 'غير محدد'); ?></p>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">الراتب:</label>
                            <p class="text-sm text-gray-900"><?php echo e($user->salary ? number_format($user->salary) . ' ريال' : 'غير محدد'); ?></p>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">تاريخ الإنشاء:</label>
                            <p class="text-sm text-gray-900"><?php echo e($user->created_at->format('Y-m-d H:i')); ?></p>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">آخر تحديث:</label>
                            <p class="text-sm text-gray-900"><?php echo e($user->updated_at->format('Y-m-d H:i')); ?></p>
                        </div>
                        <?php if($user->email_verified_at): ?>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">تاريخ تأكيد البريد:</label>
                            <p class="text-sm text-gray-900"><?php echo e($user->email_verified_at->format('Y-m-d H:i')); ?></p>
                        </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>

            <!-- User Roles -->
            <div class="mt-8 pt-6 border-t border-gray-200">
                <h3 class="text-sm font-medium text-gray-900 mb-3">الأدوار المخصصة:</h3>
                <?php if($user->roles && $user->roles->count() > 0): ?>
                    <div class="flex flex-wrap gap-2">
                        <?php $__currentLoopData = $user->roles; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $role): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                <?php echo e($role->name); ?>

                            </span>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </div>
                <?php else: ?>
                    <p class="text-sm text-gray-500">لا يوجد أدوار مخصصة لهذا المستخدم</p>
                <?php endif; ?>
            </div>

            <!-- User Permissions -->
            <?php if($user->getAllPermissions()->count() > 0): ?>
            <div class="mt-6 pt-6 border-t border-gray-200">
                <h3 class="text-sm font-medium text-gray-900 mb-3">الصلاحيات:</h3>
                <div class="flex flex-wrap gap-2">
                    <?php $__currentLoopData = $user->getAllPermissions(); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $permission): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                            <?php echo e($permission->name); ?>

                        </span>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </div>
            </div>
            <?php endif; ?>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>
<?php echo $__env->make('layouts.master', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH D:\my dehive work\resturant-pos - Copy\epis - Copy\modules\Auth\Providers/../resources/views/users/show.blade.php ENDPATH**/ ?>