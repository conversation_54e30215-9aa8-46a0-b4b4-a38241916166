<?php $__env->startSection('title', 'Payment Management'); ?>

<?php $__env->startPush('styles'); ?>
<meta name="csrf-token" content="<?php echo e(csrf_token()); ?>">
<!-- DataTables CSS -->
<link rel="stylesheet" href="https://cdn.datatables.net/1.13.7/css/jquery.dataTables.min.css">
<link rel="stylesheet" href="https://cdn.datatables.net/buttons/2.4.2/css/buttons.dataTables.min.css">
<link rel="stylesheet" href="https://cdn.datatables.net/responsive/2.5.0/css/responsive.dataTables.min.css">
<!-- Select2 CSS -->
<link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet" />
<style>
    /* Custom DataTables Tailwind Styling */
    .dataTables_wrapper {
        font-family: inherit;
    }
    
    .dataTables_wrapper .dataTables_length,
    .dataTables_wrapper .dataTables_filter,
    .dataTables_wrapper .dataTables_info,
    .dataTables_wrapper .dataTables_processing,
    .dataTables_wrapper .dataTables_paginate {
        color: #374151;
        font-size: 0.875rem;
        padding: 0.5rem 0.75rem;
    }
    .dataTables_processing{
        margin: auto
    }
    .dataTables_wrapper .dataTables_length select,
    .dataTables_wrapper .dataTables_filter input {
        border: 1px solid #d1d5db;
        border-radius: 0.375rem;
        padding: 0.375rem 0.75rem;
        font-size: 0.875rem;
    }
    
    .dataTables_wrapper .dataTables_filter input:focus {
        outline: none;
        border-color: #3b82f6;
        box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
    }
    
    .dataTables_wrapper .dataTables_paginate .paginate_button {
        padding: 0.5rem 0.75rem !important;
        margin: 0 0.125rem !important;
        border-radius: 0.375rem !important;
        border: 1px solid #d1d5db !important;
        background: white !important;
        color: #374151 !important;
        text-decoration: none !important;
    }
    
    .dataTables_wrapper .dataTables_paginate .paginate_button:hover {
        background: #f3f4f6 !important;
        border-color: #9ca3af !important;
        color: #374151 !important;
    }
    
    .dataTables_wrapper .dataTables_paginate .paginate_button.current {
        background: #3b82f6 !important;
        border-color: #3b82f6 !important;
        color: white !important;
    }
    
    .dataTables_wrapper .dataTables_paginate .paginate_button.disabled {
        background: #f9fafb !important;
        color: #9ca3af !important;
        border-color: #e5e7eb !important;
    }
    
    table.dataTable {
        border-collapse: separate !important;
        border-spacing: 0 !important;
    }
    
    table.dataTable thead th {
        background-color: #f9fafb;
        border-bottom: 1px solid #e5e7eb;
        font-weight: 600;
        color: #374151;
        padding: 0.75rem;
    }
    
    table.dataTable tbody td {
        padding: 0.75rem;
        border-bottom: 1px solid #f3f4f6;
    }
    
    table.dataTable tbody tr:hover {
        background-color: #f9fafb;
    }
    
    .select2-container--default .select2-selection--single {
        height: 42px;
        border: 1px solid #d1d5db;
        border-radius: 0.5rem;
        padding: 0.5rem;
    }
    
    .select2-container--default .select2-selection--single .select2-selection__rendered {
        line-height: 26px;
        padding-left: 0;
    }
    
    .select2-container--default .select2-selection--single .select2-selection__arrow {
        height: 40px;
    }
    
    .modal-backdrop {
        background-color: rgba(0, 0, 0, 0.5);
    }
    
    .animate-fadeIn {
        animation: fadeIn 0.3s ease-in-out;
    }
    
    @keyframes fadeIn {
        from { opacity: 0; transform: scale(0.95); }
        to { opacity: 1; transform: scale(1); }
    }
    
    .animate-slideDown {
        animation: slideDown 0.3s ease-in-out;
    }
    
    @keyframes slideDown {
        from { opacity: 0; transform: translateY(-10px); }
        to { opacity: 1; transform: translateY(0); }
    }
</style>
<?php $__env->stopPush(); ?>

<?php $__env->startSection('content'); ?>
<div class="space-y-6">
    <!-- Page Header -->
    <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between">
        <div>
            <h1 class="text-2xl font-bold text-gray-900 flex items-center">
                <i class="fas fa-credit-card mr-3"></i>
                Payment Management
            </h1>
            <p class="text-sm text-gray-600 mt-1">Manage and track all payment transactions</p>
        </div>
        <div class="mt-4 sm:mt-0">
            <button type="button" id="new-payment-btn" class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                <i class="fas fa-plus mr-2"></i>
                New Payment
            </button>
        </div>
    </div>

    <!-- Filters Section -->
    <div class="bg-white shadow rounded-lg">
        <div class="px-6 py-4 border-b border-gray-200">
            <h3 class="text-lg font-medium text-gray-900">Filters</h3>
        </div>
        <div class="p-6">
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                <!-- Status Filter -->
                <div>
                    <label for="status-filter" class="block text-sm font-medium text-gray-700 mb-1">Status</label>
                    <select id="status-filter" class="w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                        <option value="">All Statuses</option>
                        <option value="completed">Completed</option>
                        <option value="pending">Pending</option>
                        <option value="failed">Failed</option>
                        <option value="cancelled">Cancelled</option>
                        <option value="refunded">Refunded</option>
                    </select>
                </div>

                <!-- Payment Method Filter -->
                <div>
                    <label for="payment-method-filter" class="block text-sm font-medium text-gray-700 mb-1">Payment Method</label>
                    <select id="payment-method-filter" class="w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                        <option value="">All Methods</option>
                        <!-- Payment methods will be loaded dynamically -->
                    </select>
                </div>

                <!-- Date From -->
                <div>
                    <label for="date-from" class="block text-sm font-medium text-gray-700 mb-1">Date From</label>
                    <input type="date" id="date-from" class="w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                </div>

                <!-- Date To -->
                <div>
                    <label for="date-to" class="block text-sm font-medium text-gray-700 mb-1">Date To</label>
                    <input type="date" id="date-to" class="w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                </div>

                <!-- Amount From -->
                <div>
                    <label for="amount-from" class="block text-sm font-medium text-gray-700 mb-1">Amount From</label>
                    <input type="number" id="amount-from" step="0.01" min="0" class="w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500" placeholder="0.00">
                </div>

                <!-- Amount To -->
                <div>
                    <label for="amount-to" class="block text-sm font-medium text-gray-700 mb-1">Amount To</label>
                    <input type="number" id="amount-to" step="0.01" min="0" class="w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500" placeholder="0.00">
                </div>
            </div>
            
            <div class="mt-4 flex space-x-3">
                <button type="button" id="apply-filters" class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                    <i class="fas fa-filter mr-2"></i>
                    Apply Filters
                </button>
                <button type="button" id="clear-filters" class="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                    <i class="fas fa-times mr-2"></i>
                    Clear
                </button>
            </div>
        </div>
    </div>

    <!-- Payments Table -->
    <div class="bg-white shadow rounded-lg overflow-hidden">
        <div class="px-6 py-4 border-b border-gray-200">
            <h3 class="text-lg font-medium text-gray-900">Payments</h3>
        </div>
        <div class="overflow-x-auto">
            <table id="payments-table" class="min-w-full divide-y divide-gray-200">
                <thead>
                    <tr>
                        <th>Payment #</th>
                        <th>Transaction #</th>
                        <th>Order #</th>
                        <th>Payment Method</th>
                        <th>Amount</th>
                        <th>Status</th>
                        <th>Reference</th>
                        <th>Date</th>
                        <th>Processed By</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    <!-- Data will be loaded via DataTables -->
                </tbody>
            </table>
        </div>
    </div>
</div>

<!-- Payment Details Modal -->
<div id="payment-details-modal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden z-50">
    <div class="relative top-20 mx-auto p-5 border w-11/12 md:w-3/4 lg:w-1/2 shadow-lg rounded-md bg-white">
        <div class="mt-3">
            <div class="flex items-center justify-between mb-4">
                <h3 class="text-lg font-medium text-gray-900">Payment Details</h3>
                <button type="button" class="close-modal text-gray-400 hover:text-gray-600">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div id="payment-details-content">
                <!-- Payment details will be loaded here -->
            </div>
        </div>
    </div>
</div>

<!-- Cancel Payment Modal -->
<div id="cancel-payment-modal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden z-50">
    <div class="relative top-20 mx-auto p-5 border w-11/12 md:w-1/2 shadow-lg rounded-md bg-white">
        <div class="mt-3">
            <div class="flex items-center justify-between mb-4">
                <h3 class="text-lg font-medium text-gray-900">Cancel Payment</h3>
                <button type="button" class="close-modal text-gray-400 hover:text-gray-600">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <form id="cancel-payment-form" method="POST">
                <?php echo csrf_field(); ?>
                <div class="mb-4">
                    <label for="cancel-reason" class="block text-sm font-medium text-gray-700 mb-2">Reason for cancellation</label>
                    <textarea id="cancel-reason" name="reason" rows="3" class="w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500" placeholder="Enter reason for cancellation..."></textarea>
                </div>
                <div class="flex justify-end space-x-3">
                    <button type="button" class="close-modal px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50">
                        Cancel
                    </button>
                    <button type="submit" class="px-4 py-2 bg-red-600 text-white rounded-md text-sm font-medium hover:bg-red-700">
                        Cancel Payment
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Refund Payment Modal -->
<div id="refund-payment-modal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden z-50">
    <div class="relative top-20 mx-auto p-5 border w-11/12 md:w-1/2 shadow-lg rounded-md bg-white">
        <div class="mt-3">
            <div class="flex items-center justify-between mb-4">
                <h3 class="text-lg font-medium text-gray-900">Refund Payment</h3>
                <button type="button" class="close-modal text-gray-400 hover:text-gray-600">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <form id="refund-payment-form" method="POST">
                <?php echo csrf_field(); ?>
                <div class="mb-4">
                    <label for="refund-amount" class="block text-sm font-medium text-gray-700 mb-2">Refund Amount</label>
                    <input type="number" id="refund-amount" name="refund_amount" step="0.01" min="0" class="w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500" placeholder="0.00">
                </div>
                <div class="mb-4">
                    <label for="refund-reason" class="block text-sm font-medium text-gray-700 mb-2">Reason for refund</label>
                    <textarea id="refund-reason" name="reason" rows="3" class="w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500" placeholder="Enter reason for refund..."></textarea>
                </div>
                <div class="flex justify-end space-x-3">
                    <button type="button" class="close-modal px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50">
                        Cancel
                    </button>
                    <button type="submit" class="px-4 py-2 bg-red-600 text-white rounded-md text-sm font-medium hover:bg-red-700">
                        Process Refund
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Create Payment Modal -->
<div id="create-payment-modal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden z-50">
    <div class="relative top-10 mx-auto p-5 border w-11/12 md:w-4/5 lg:w-3/4 xl:w-2/3 shadow-lg rounded-md bg-white max-h-screen overflow-y-auto">
        <div class="mt-3">
            <div class="flex items-center justify-between mb-4">
                <h3 class="text-lg font-medium text-gray-900">
                    <i class="fas fa-credit-card mr-2"></i>
                    Create New Payment
                </h3>
                <button type="button" class="close-modal text-gray-400 hover:text-gray-600">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            
            <!-- Payment Form -->
            <form id="create-payment-form" action="<?php echo e(route('payments.store')); ?>" method="POST">
                <?php echo csrf_field(); ?>
                
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <!-- Transaction Selection -->
                    <div class="md:col-span-2">
                        <label for="modal-transaction-id" class="block text-sm font-medium text-gray-700 mb-2">
                            Transaction <span class="text-red-500">*</span>
                        </label>
                        <select id="modal-transaction-id" name="transaction_id" required class="w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                            <option value="">Select a transaction</option>
                            <?php $__currentLoopData = $dueTransactions ?? []; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $transaction): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <option value="<?php echo e($transaction->id); ?>" data-due="<?php echo e($transaction->due_amount); ?>">
                                    <?php echo e($transaction->transaction_number); ?> - Due: $<?php echo e(number_format($transaction->due_amount, 2)); ?>

                                </option>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </select>
                    </div>

                    <!-- Payment Method -->
                    <div>
                        <label for="modal-payment-method-id" class="block text-sm font-medium text-gray-700 mb-2">
                            Payment Method <span class="text-red-500">*</span>
                        </label>
                        <select id="modal-payment-method-id" name="payment_method_id" required class="w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                            <option value="">Select payment method</option>
                            <?php $__currentLoopData = $paymentMethods ?? []; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $method): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <option value="<?php echo e($method->id); ?>"><?php echo e($method->name); ?></option>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </select>
                    </div>

                    <!-- Amount Received -->
                    <div>
                        <label for="modal-amount-received" class="block text-sm font-medium text-gray-700 mb-2">
                            Amount Received <span class="text-red-500">*</span>
                        </label>
                        <div class="relative">
                            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                <span class="text-gray-500 sm:text-sm">$</span>
                            </div>
                            <input type="number" id="modal-amount-received" name="amount_received" step="0.01" min="0" required 
                                   class="w-full pl-7 border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500" 
                                   placeholder="0.00">
                        </div>
                    </div>

                    <!-- Payment Amount (calculated) -->
                    <div>
                        <label for="modal-amount" class="block text-sm font-medium text-gray-700 mb-2">
                            Payment Amount <span class="text-red-500">*</span>
                        </label>
                        <div class="relative">
                            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                <span class="text-gray-500 sm:text-sm">$</span>
                            </div>
                            <input type="number" id="modal-amount" name="amount" step="0.01" min="0" required readonly
                                   class="w-full pl-7 border border-gray-300 rounded-md px-3 py-2 text-sm bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500" 
                                   placeholder="0.00">
                        </div>
                        <p class="mt-1 text-xs text-gray-500">Automatically calculated based on due amount and amount received</p>
                    </div>

                    <!-- Change Amount (calculated) -->
                    <div>
                        <label for="modal-change-amount" class="block text-sm font-medium text-gray-700 mb-2">
                            Change Amount
                        </label>
                        <div class="relative">
                            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                <span class="text-gray-500 sm:text-sm">$</span>
                            </div>
                            <input type="number" id="modal-change-amount" name="change_amount" step="0.01" min="0" readonly
                                   value="0.00"
                                   class="w-full pl-7 border border-gray-300 rounded-md px-3 py-2 text-sm bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500" 
                                   placeholder="0.00">
                        </div>
                        <p class="mt-1 text-xs text-gray-500">Automatically calculated when amount received exceeds due amount</p>
                    </div>

                    <!-- Reference Number -->
                    <div>
                        <label for="modal-reference-number" class="block text-sm font-medium text-gray-700 mb-2">
                            Reference Number
                        </label>
                        <input type="text" id="modal-reference-number" name="reference_number" 
                               class="w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500" 
                               placeholder="Enter reference number">
                    </div>

                    <!-- Notes -->
                    <div class="md:col-span-2">
                        <label for="modal-notes" class="block text-sm font-medium text-gray-700 mb-2">
                            Notes
                        </label>
                        <textarea id="modal-notes" name="notes" rows="3" 
                                  class="w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500" 
                                  placeholder="Enter any additional notes..."></textarea>
                    </div>
                </div>

                <!-- Transaction Details -->
                <div id="modal-transaction-details" class="mt-6 bg-gray-50 p-4 rounded-lg hidden">
                    <h4 class="text-sm font-medium text-gray-900 mb-3">Transaction Details</h4>
                    <div class="grid grid-cols-2 gap-4 text-sm">
                        <div>
                            <span class="text-gray-600">Transaction Number:</span>
                            <span id="modal-detail-transaction-number" class="font-medium text-gray-900 ml-2">-</span>
                        </div>
                        <div>
                            <span class="text-gray-600">Total Amount:</span>
                            <span id="modal-detail-total-amount" class="font-medium text-gray-900 ml-2">$0.00</span>
                        </div>
                        <div>
                            <span class="text-gray-600">Paid Amount:</span>
                            <span id="modal-detail-paid-amount" class="font-medium text-gray-900 ml-2">$0.00</span>
                        </div>
                        <div>
                            <span class="text-gray-600">Due Amount:</span>
                            <span id="modal-detail-due-amount" class="font-medium text-red-600 ml-2">$0.00</span>
                        </div>
                        <div>
                            <span class="text-gray-600">Status:</span>
                            <span id="modal-detail-status" class="ml-2">-</span>
                        </div>
                        <div>
                            <span class="text-gray-600">Order Number:</span>
                            <span id="modal-detail-order-number" class="font-medium text-gray-900 ml-2">-</span>
                        </div>
                    </div>
                </div>

                <!-- Payment Calculation Summary -->
                <div id="modal-payment-summary" class="mt-6 bg-blue-50 p-4 rounded-lg border border-blue-200 hidden">
                    <h4 class="text-sm font-medium text-blue-900 mb-3">Payment Summary</h4>
                    <div class="grid grid-cols-3 gap-4 text-sm">
                        <div class="text-center">
                            <div class="text-blue-600 font-medium">Amount Received</div>
                            <div id="modal-summary-received" class="text-lg font-bold text-blue-900">$0.00</div>
                        </div>
                        <div class="text-center">
                            <div class="text-green-600 font-medium">Payment Amount</div>
                            <div id="modal-summary-payment" class="text-lg font-bold text-green-900">$0.00</div>
                        </div>
                        <div class="text-center">
                            <div class="text-orange-600 font-medium">Change</div>
                            <div id="modal-summary-change" class="text-lg font-bold text-orange-900">$0.00</div>
                        </div>
                    </div>
                    <div class="mt-3 pt-3 border-t border-blue-200">
                        <div class="flex justify-between text-sm">
                            <span class="text-blue-600">Remaining Due:</span>
                            <span id="modal-summary-remaining" class="font-medium text-blue-900">$0.00</span>
                        </div>
                    </div>
                </div>

                <!-- Form Actions -->
                <div class="mt-6 flex justify-end space-x-3">
                    <button type="button" class="close-modal px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                        Cancel
                    </button>
                    <button type="submit" class="px-4 py-2 bg-blue-600 text-white rounded-md text-sm font-medium hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                        <i class="fas fa-credit-card mr-2"></i>
                        Process Payment
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('scripts'); ?>
<!-- DataTables JS -->
<script src="https://cdn.datatables.net/1.13.7/js/jquery.dataTables.min.js"></script>
<script src="https://cdn.datatables.net/responsive/2.5.0/js/dataTables.responsive.min.js"></script>

<!-- Select2 JS -->
<script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>

<script>
$(document).ready(function() {
    // Initialize Select2
    $('#payment-method-filter').select2({
        placeholder: 'Select payment method',
        allowClear: true
    });

    // Initialize modal Select2
    $('#modal-transaction-id').select2({
        placeholder: 'Select a transaction',
        allowClear: true,
        width: '100%',
        dropdownParent: $('#create-payment-modal')
    });

    $('#modal-payment-method-id').select2({
        placeholder: 'Select payment method',
        allowClear: true,
        width: '100%',
        dropdownParent: $('#create-payment-modal')
    });

    // Initialize DataTable
    var table = $('#payments-table').DataTable({
        processing: true,
        serverSide: true,
        responsive: true,
        ajax: {
            url: '<?php echo e(route("payments.index")); ?>',
            data: function(d) {
                d.status = $('#status-filter').val();
                d.payment_method = $('#payment-method-filter').val();
                d.date_from = $('#date-from').val();
                d.date_to = $('#date-to').val();
                d.amount_from = $('#amount-from').val();
                d.amount_to = $('#amount-to').val();
            }
        },
        columns: [
            { data: 'payment_number', name: 'payment_number' },
            { data: 'transaction_number', name: 'transaction_number' },
            { data: 'order_number', name: 'order_number' },
            { data: 'payment_method_badge', name: 'payment_method', orderable: false },
            { data: 'formatted_amount', name: 'amount' },
            { data: 'status_badge', name: 'status', orderable: false },
            { data: 'reference_number', name: 'reference_number' },
            { data: 'formatted_date', name: 'payment_date' },
            { data: 'processed_by', name: 'processed_by' },
            { data: 'action', name: 'action', orderable: false, searchable: false }
        ],
        order: [[7, 'desc']], // Order by date descending
        pageLength: 25,
        language: {
            processing: "Loading payments...",
            emptyTable: "No payments found",
            zeroRecords: "No matching payments found"
        }
    });

    // Apply filters
    $('#apply-filters').on('click', function() {
        table.ajax.reload();
    });

    // Clear filters
    $('#clear-filters').on('click', function() {
        $('#status-filter').val('');
        $('#payment-method-filter').val('').trigger('change');
        $('#date-from').val('');
        $('#date-to').val('');
        $('#amount-from').val('');
        $('#amount-to').val('');
        table.ajax.reload();
    });

    // View payment details
    $(document).on('click', '.view-payment-btn', function() {
        var paymentId = $(this).data('id');
        
        // Load payment details via AJAX
        $.get('<?php echo e(route("payments.show", ":id")); ?>'.replace(':id', paymentId))
            .done(function(response) {
                $('#payment-details-content').html(response);
                $('#payment-details-modal').removeClass('hidden');
            })
            .fail(function() {
                Swal.fire('Error', 'Failed to load payment details', 'error');
            });
    });

    // Cancel payment
    $(document).on('click', '.cancel-payment-btn', function() {
        var paymentId = $(this).data('id');
        $('#cancel-payment-form').attr('action', '<?php echo e(route("payments.cancel", ":id")); ?>'.replace(':id', paymentId));
        $('#cancel-payment-modal').removeClass('hidden');
    });

    // Refund payment
    $(document).on('click', '.refund-payment-btn', function() {
        var paymentId = $(this).data('id');
        $('#refund-payment-form').attr('action', '<?php echo e(route("payments.refund", ":id")); ?>'.replace(':id', paymentId));
        $('#refund-payment-modal').removeClass('hidden');
    });

    // Close modals
    $(document).on('click', '.close-modal', function() {
        $(this).closest('.fixed').addClass('hidden');
    });

    // Close modal when clicking outside
    $(document).on('click', '.fixed', function(e) {
        if (e.target === this) {
            $(this).addClass('hidden');
        }
    });

    // Open create payment modal
    $('#new-payment-btn').on('click', function() {
        resetCreatePaymentForm();
        $('#create-payment-modal').removeClass('hidden');
    });

    // Create payment modal functionality
    let currentModalTransaction = null;

    // Handle transaction selection in modal
    $('#modal-transaction-id').on('change', function() {
        const transactionId = $(this).val();
        
        if (transactionId) {
            // Fetch transaction details
            $.ajax({
                url: `/transactions/${transactionId}`,
                method: 'GET',
                success: function(response) {
                    if (response.success) {
                        currentModalTransaction = response.data;
                        
                        // Update transaction details
                        $('#modal-detail-transaction-number').text(currentModalTransaction.transaction_number || '-');
                        $('#modal-detail-total-amount').text('$' + parseFloat(currentModalTransaction.total_amount || 0).toFixed(2));
                        $('#modal-detail-paid-amount').text('$' + parseFloat(currentModalTransaction.paid_amount || 0).toFixed(2));
                        $('#modal-detail-due-amount').text('$' + parseFloat(currentModalTransaction.due_amount || 0).toFixed(2));
                        $('#modal-detail-status').text(currentModalTransaction.status || '-');
                        $('#modal-detail-order-number').text(currentModalTransaction.order?.order_number || '-');
                        
                        // Show transaction details
                        $('#modal-transaction-details').removeClass('hidden');
                        
                        // Reset calculations
                        calculateModalPayment();
                    }
                },
                error: function() {
                    alert('Error fetching transaction details');
                }
            });
        } else {
            // Hide transaction details and clear form data
            currentModalTransaction = null;
            $('#modal-transaction-details').addClass('hidden');
            $('#modal-payment-summary').addClass('hidden');
            $('#modal-amount-received').val('');
            $('#modal-amount').val('');
            $('#modal-change-amount').val('0.00');
        }
    });

    // Handle amount received input in modal
    $('#modal-amount-received').on('input', function() {
        calculateModalPayment();
    });

    // Calculate payment amounts for modal
    function calculateModalPayment() {
        if (!currentModalTransaction) {
            return;
        }

        const amountReceived = parseFloat($('#modal-amount-received').val()) || 0;
        const dueAmount = parseFloat(currentModalTransaction.due_amount || 0);
        
        let paymentAmount = 0;
        let changeAmount = 0;
        let remainingDue = dueAmount;

        if (amountReceived > 0) {
            if (amountReceived >= dueAmount) {
                // Full payment or overpayment
                paymentAmount = dueAmount;
                changeAmount = amountReceived - dueAmount;
                remainingDue = 0;
            } else {
                // Partial payment
                paymentAmount = amountReceived;
                changeAmount = 0;
                remainingDue = dueAmount - amountReceived;
            }

            // Update form fields
            $('#modal-amount').val(paymentAmount.toFixed(2));
            $('#modal-change-amount').val(changeAmount.toFixed(2));

            // Update summary
            $('#modal-summary-received').text('$' + amountReceived.toFixed(2));
            $('#modal-summary-payment').text('$' + paymentAmount.toFixed(2));
            $('#modal-summary-change').text('$' + changeAmount.toFixed(2));
            $('#modal-summary-remaining').text('$' + remainingDue.toFixed(2));

            // Show payment summary
            $('#modal-payment-summary').removeClass('hidden');
        } else {
            // Reset when no amount received
            $('#modal-amount').val('');
            $('#modal-change-amount').val('0.00');
            $('#modal-payment-summary').addClass('hidden');
        }
    }

    // Reset create payment form
    function resetCreatePaymentForm() {
        // Reset without triggering change events to prevent infinite loop
        $('#modal-transaction-id').val('');
        $('#modal-payment-method-id').val('');
        $('#modal-amount-received').val('');
        $('#modal-amount').val('');
        $('#modal-change-amount').val('0.00');
        $('#modal-reference-number').val('');
        $('#modal-notes').val('');
        $('#modal-transaction-details').addClass('hidden');
        $('#modal-payment-summary').addClass('hidden');
        currentModalTransaction = null;
    }

    // Handle create payment form submission
    $('#create-payment-form').on('submit', function(e) {
        e.preventDefault();
        
        const amountReceived = parseFloat($('#modal-amount-received').val()) || 0;
        const paymentAmount = parseFloat($('#modal-amount').val()) || 0;
        
        if (!currentModalTransaction) {
            alert('Please select a transaction');
            return false;
        }
        
        if (amountReceived <= 0) {
            alert('Amount received must be greater than 0');
            return false;
        }
        
        if (paymentAmount <= 0) {
            alert('Payment amount must be greater than 0');
            return false;
        }

        if (!$('#modal-payment-method-id').val()) {
            alert('Please select a payment method');
            return false;
        }

        // Show loading state
        const submitBtn = $(this).find('button[type="submit"]');
        const originalText = submitBtn.html();
        submitBtn.prop('disabled', true).html('<i class="fas fa-spinner fa-spin mr-2"></i>Processing...');

        // Submit form
        $.post($(this).attr('action'), $(this).serialize())
            .done(function(response) {
                $('#create-payment-modal').addClass('hidden');
                table.ajax.reload();
                Swal.fire('Success', 'Payment processed successfully', 'success');
                resetCreatePaymentForm();
            })
            .fail(function(xhr) {
                let errorMessage = 'Failed to process payment';
                if (xhr.responseJSON && xhr.responseJSON.message) {
                    errorMessage = xhr.responseJSON.message;
                }
                Swal.fire('Error', errorMessage, 'error');
            })
            .always(function() {
                submitBtn.prop('disabled', false).html(originalText);
            });
    });

    // Handle form submissions
    $('#cancel-payment-form').on('submit', function(e) {
        e.preventDefault();
        
        $.post($(this).attr('action'), $(this).serialize())
            .done(function(response) {
                $('#cancel-payment-modal').addClass('hidden');
                table.ajax.reload();
                Swal.fire('Success', 'Payment cancelled successfully', 'success');
            })
            .fail(function() {
                Swal.fire('Error', 'Failed to cancel payment', 'error');
            });
    });

    $('#refund-payment-form').on('submit', function(e) {
        e.preventDefault();
        
        $.post($(this).attr('action'), $(this).serialize())
            .done(function(response) {
                $('#refund-payment-modal').addClass('hidden');
                table.ajax.reload();
                Swal.fire('Success', 'Payment refunded successfully', 'success');
            })
            .fail(function() {
                Swal.fire('Error', 'Failed to process refund', 'error');
            });
    });
});
</script>
<?php $__env->stopPush(); ?>
<?php echo $__env->make('layouts.master', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH D:\my dehive work\resturant-pos - Copy\epis - Copy\modules\Transaction\Providers/../resources/views/payments/index.blade.php ENDPATH**/ ?>