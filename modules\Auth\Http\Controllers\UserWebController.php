<?php

namespace Modules\Auth\Http\Controllers;

use App\Http\Controllers\Controller;
use App\Models\User;
use App\Models\Tenant;
use App\Models\Branch;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\DB;
use Yajra\DataTables\Facades\DataTables;
use Spatie\Permission\Models\Role;
use Spatie\Permission\Models\Permission;

class UserWebController extends Controller
{
    /**
     * Display a listing of users.
     */
    public function index()
    {
        $tenants = Tenant::where('status', 'active')->get();
        $branches = Branch::where('status', 'active')->get();
        $roles = Role::all();
        
        return view('Auth::users.index', compact('tenants', 'branches', 'roles'));
    }

    /**
     * Show the form for creating a new user.
     */
    public function create()
    {
        $tenants = Tenant::where('status', 'active')->get();
        $branches = Branch::where('status', 'active')->get();
        $roles = Role::all();
        
        return view('Auth::users.create', compact('tenants', 'branches', 'roles'));
    }

    /**
     * Store a newly created user.
     */
    public function store(Request $request)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'email' => 'required|email|unique:users,email',
            'password' => 'required|string|min:8|confirmed',
            'tenant_id' => 'required|exists:tenants,id',
            'branch_id' => 'nullable|exists:branches,id',
            'employee_id' => 'nullable|string|unique:users,employee_id',
            'position' => 'nullable|string|max:100',
            'department' => 'nullable|string|max:100',
            'hourly_rate' => 'nullable|numeric|min:0',
            'salary' => 'nullable|numeric|min:0',
            'base_salary' => 'nullable|numeric|min:0',
            'roles' => 'nullable|array',
            'roles.*' => 'exists:roles,name',
        ]);

        try {
            DB::beginTransaction();

            $userData = $request->except(['password_confirmation', 'roles']);
            $userData['password'] = Hash::make($request->password);
            $userData['is_active'] = true;
            $userData['tenant_id'] = $request->tenant_id;

            $user = User::create($userData);

            // Assign roles if provided
            if ($request->filled('roles')) {
                $user->assignRole($request->roles);
            }

            DB::commit();
            
            if (request()->ajax()) {
                return response()->json([
                    'success' => true,
                    'message' => 'تم إنشاء المستخدم بنجاح'
                ]);
            }
            
            return redirect()->route('users.index')
                ->with('success', 'تم إنشاء المستخدم بنجاح');
        } catch (\Exception $e) {
            DB::rollBack();
            
            if (request()->ajax()) {
                return response()->json([
                    'success' => false,
                    'message' => 'حدث خطأ أثناء إنشاء المستخدم: ' . $e->getMessage(),
                    'errors' => $e->getMessage()
                ], 422);
            }
            
            return back()->withInput()
                ->with('error', 'حدث خطأ أثناء إنشاء المستخدم: ' . $e->getMessage());
        }
    }

    /**
     * Display the specified user.
     */
    public function show(User $user)
    {
        $user->load(['tenant', 'branch', 'roles', 'permissions']);
        
        if (request()->ajax()) {
            return response()->json([
                'id' => $user->id,
                'name' => $user->name,
                'email' => $user->email,
                'tenant_id' => $user->tenant_id,
                'branch_id' => $user->branch_id,
                'employee_id' => $user->employee_id,
                'position' => $user->position,
                'department' => $user->department,
                'hourly_rate' => $user->hourly_rate,
                'salary' => $user->salary,
                'base_salary' => $user->base_salary,
                'is_active' => $user->is_active,
                'roles' => $user->roles,
                'tenant' => $user->tenant,
                'branch' => $user->branch,
            ]);
        }
        
        return view('Auth::users.show', compact('user'));
    }

    /**
     * Show the form for editing the specified user.
     */
    public function edit(User $user)
    {
        $tenants = Tenant::where('status', 'active')->get();
        $branches = Branch::where('status', 'active')->get();
        $roles = Role::all();
        $userRoles = $user->roles->pluck('name')->toArray();
        
        return view('Auth::users.edit', compact('user', 'tenants', 'branches', 'roles', 'userRoles'));
    }

    /**
     * Update the specified user.
     */
    public function update(Request $request, User $user)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'email' => 'required|email|unique:users,email,' . $user->id,
            'password' => 'nullable|string|min:8|confirmed',
            'tenant_id' => 'required|exists:tenants,id',
            'branch_id' => 'nullable|exists:branches,id',
            'employee_id' => 'nullable|string|unique:users,employee_id,' . $user->id,
            'position' => 'nullable|string|max:100',
            'department' => 'nullable|string|max:100',
            'hourly_rate' => 'nullable|numeric|min:0',
            'salary' => 'nullable|numeric|min:0',
            'base_salary' => 'nullable|numeric|min:0',
            'roles' => 'nullable|array',
            'roles.*' => 'exists:roles,name',
        ]);

        try {
            DB::beginTransaction();

            $userData = $request->except(['password_confirmation', 'roles']);
            
            if ($request->filled('password')) {
                $userData['password'] = Hash::make($request->password);
            } else {
                unset($userData['password']);
            }

            $user->update($userData);

            // Sync roles
            if ($request->has('roles')) {
                $user->syncRoles($request->roles ?? []);
            }

            DB::commit();
            
            if (request()->ajax()) {
                return response()->json([
                    'success' => true,
                    'message' => 'تم تحديث بيانات المستخدم بنجاح'
                ]);
            }
            
            return redirect()->route('users.show', $user)
                ->with('success', 'تم تحديث بيانات المستخدم بنجاح');
        } catch (\Exception $e) {
            DB::rollBack();
            
            if (request()->ajax()) {
                return response()->json([
                    'success' => false,
                    'message' => 'حدث خطأ أثناء تحديث المستخدم: ' . $e->getMessage(),
                    'errors' => $e->getMessage()
                ], 422);
            }
            
            return back()->withInput()
                ->with('error', 'حدث خطأ أثناء تحديث المستخدم: ' . $e->getMessage());
        }
    }

    /**
     * Remove the specified user.
     */
    public function destroy(User $user)
    {
        try {
            // Check if user has any related data that prevents deletion
            // Add your business logic here

            $user->delete();
            
            if (request()->ajax()) {
                return response()->json([
                    'success' => true,
                    'message' => 'تم حذف المستخدم بنجاح'
                ]);
            }
            
            return redirect()->route('users.index')
                ->with('success', 'تم حذف المستخدم بنجاح');
        } catch (\Exception $e) {
            if (request()->ajax()) {
                return response()->json([
                    'success' => false,
                    'message' => 'حدث خطأ أثناء حذف المستخدم: ' . $e->getMessage()
                ], 500);
            }
            
            return back()->with('error', 'حدث خطأ أثناء حذف المستخدم: ' . $e->getMessage());
        }
    }

    /**
     * Activate user.
     */
    public function activate(User $user)
    {
        try {
            $user->update(['is_active' => true]);
            
            if (request()->ajax()) {
                return response()->json([
                    'success' => true,
                    'message' => 'تم تفعيل المستخدم بنجاح'
                ]);
            }
            
            return back()->with('success', 'تم تفعيل المستخدم بنجاح');
        } catch (\Exception $e) {
            if (request()->ajax()) {
                return response()->json([
                    'success' => false,
                    'message' => 'حدث خطأ أثناء تفعيل المستخدم: ' . $e->getMessage()
                ], 500);
            }
            
            return back()->with('error', 'حدث خطأ أثناء تفعيل المستخدم: ' . $e->getMessage());
        }
    }

    /**
     * Deactivate user.
     */
    public function deactivate(User $user)
    {
        try {
            $user->update(['is_active' => false]);
            
            if (request()->ajax()) {
                return response()->json([
                    'success' => true,
                    'message' => 'تم إلغاء تفعيل المستخدم بنجاح'
                ]);
            }
            
            return back()->with('success', 'تم إلغاء تفعيل المستخدم بنجاح');
        } catch (\Exception $e) {
            if (request()->ajax()) {
                return response()->json([
                    'success' => false,
                    'message' => 'حدث خطأ أثناء إلغاء تفعيل المستخدم: ' . $e->getMessage()
                ], 500);
            }
            
            return back()->with('error', 'حدث خطأ أثناء إلغاء تفعيل المستخدم: ' . $e->getMessage());
        }
    }

    /**
     * Reset user password.
     */
    public function resetPassword(User $user)
    {
        try {
            $newPassword = 'password123'; // You might want to generate a random password
            $user->update(['password' => Hash::make($newPassword)]);
            
            if (request()->ajax()) {
                return response()->json([
                    'success' => true,
                    'message' => 'تم إعادة تعيين كلمة المرور بنجاح. كلمة المرور الجديدة: ' . $newPassword
                ]);
            }
            
            return back()->with('success', 'تم إعادة تعيين كلمة المرور بنجاح. كلمة المرور الجديدة: ' . $newPassword);
        } catch (\Exception $e) {
            if (request()->ajax()) {
                return response()->json([
                    'success' => false,
                    'message' => 'حدث خطأ أثناء إعادة تعيين كلمة المرور: ' . $e->getMessage()
                ], 500);
            }
            
            return back()->with('error', 'حدث خطأ أثناء إعادة تعيين كلمة المرور: ' . $e->getMessage());
        }
    }

    /**
     * Get users data for DataTable.
     */
    public function getUsersData(Request $request)
    {
        $query = User::with(['tenant', 'branch', 'roles'])
            ->select([
                'id',
                'name', 
                'email',
                'tenant_id',
                'branch_id',
                'salary',
                'is_active',
                // 'email_verified_at',
                // 'created_at',
                // 'updated_at'
            ]);
        // Apply filters
        if ($request->filled('tenant_id')) {
            $query->where('tenant_id', $request->tenant_id);
        }

        if ($request->filled('branch_id')) {
            $query->where('branch_id', $request->branch_id);
        }

        if ($request->filled('role')) {
            $query->whereHas('roles', function ($q) use ($request) {
                $q->where('name', $request->role);
            });
        }

        if ($request->filled('is_active')) {
            $query->where('is_active', $request->is_active);
        }

        return DataTables::of($query)
            ->addIndexColumn()
            ->addColumn('tenant_name', function ($user) {
                return $user->tenant ? $user->tenant->name : '-';
            })
            ->addColumn('branch_name', function ($user) {
                return $user->branch ? $user->branch->name : '-';
            })
            ->addColumn('roles', function ($user) {
                $roles = $user->roles->pluck('name')->toArray();
                $rolesHtml = '';
                foreach ($roles as $role) {
                    $rolesHtml .= '<span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800 mr-1">' . $role . '</span>';
                }
                return $rolesHtml ?: '<span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">لا يوجد أدوار</span>';
            })
            ->addColumn('status_badge', function ($user) {
                return $user->is_active 
                    ? '<span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">نشط</span>' 
                    : '<span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">غير نشط</span>';
            })

           ->addColumn('action', function ($user) {
                $actions = '<div class="flex items-center space-x-1">';
                $actions .= '<a href="' . route('users.show', $user) . '" class="inline-flex items-center px-2 py-1 text-xs font-medium text-blue-600 bg-blue-100 rounded hover:bg-blue-200 transition-colors duration-200" title="عرض"><i class="fas fa-eye"></i></a>';
                $actions .= '<button type="button" class="inline-flex items-center px-2 py-1 text-xs font-medium text-indigo-600 bg-indigo-100 rounded hover:bg-indigo-200 transition-colors duration-200" onclick="editUser(' . $user->id . ')" title="تعديل"><i class="fas fa-edit"></i></button>';
                $actions .= '<button type="button" class="inline-flex items-center px-2 py-1 text-xs font-medium text-purple-600 bg-purple-100 rounded hover:bg-purple-200 transition-colors duration-200" onclick="assignRoles(' . $user->id . ')" title="تعيين أدوار"><i class="fas fa-users"></i></button>';
                
                if ($user->is_active) {
                    $actions .= '<button type="button" class="inline-flex items-center px-2 py-1 text-xs font-medium text-gray-600 bg-gray-100 rounded hover:bg-gray-200 transition-colors duration-200" onclick="changeStatus(' . $user->id . ', \'deactivate\')" title="إلغاء تفعيل"><i class="fas fa-ban"></i></button>';
                } else {
                    $actions .= '<button type="button" class="inline-flex items-center px-2 py-1 text-xs font-medium text-green-600 bg-green-100 rounded hover:bg-green-200 transition-colors duration-200" onclick="changeStatus(' . $user->id . ', \'activate\')" title="تفعيل"><i class="fas fa-check"></i></button>';
                }
                
                $actions .= '<button type="button" class="inline-flex items-center px-2 py-1 text-xs font-medium text-yellow-600 bg-yellow-100 rounded hover:bg-yellow-200 transition-colors duration-200" onclick="resetPassword(' . $user->id . ')" title="إعادة تعيين كلمة المرور"><i class="fas fa-key"></i></button>';
                $actions .= '<button type="button" class="inline-flex items-center px-2 py-1 text-xs font-medium text-red-600 bg-red-100 rounded hover:bg-red-200 transition-colors duration-200" onclick="deleteUser(' . $user->id . ')" title="حذف"><i class="fas fa-trash"></i></button>';
                $actions .= '</div>';
                
                return $actions;
            })

            ->rawColumns(['roles', 'status_badge', 'action'])
            ->make(true);
    }

    /**
     * Show user roles page.
     */
    public function roles(User $user)
    {
        $roles = Role::all();
        $userRoles = $user->roles->pluck('name')->toArray();
        
        if (request()->ajax()) {
            return response()->json([
                'user' => $user,
                'roles' => $roles,
                'userRoles' => $userRoles
            ]);
        }
        
        return view('Auth::users.roles', compact('user', 'roles', 'userRoles'));
    }

    /**
     * Assign roles to user.
     */
    public function assignRoles(Request $request, User $user)
    {
        $request->validate([
            'roles' => 'nullable|array',
            'roles.*' => 'exists:roles,name',
        ]);

        try {
            $user->syncRoles($request->roles ?? []);
            
            if (request()->ajax()) {
                return response()->json([
                    'success' => true,
                    'message' => 'تم تحديث أدوار المستخدم بنجاح'
                ]);
            }
            
            return back()->with('success', 'تم تحديث أدوار المستخدم بنجاح');
        } catch (\Exception $e) {
            if (request()->ajax()) {
                return response()->json([
                    'success' => false,
                    'message' => 'حدث خطأ أثناء تحديث الأدوار: ' . $e->getMessage()
                ], 500);
            }
            
            return back()->with('error', 'حدث خطأ أثناء تحديث الأدوار: ' . $e->getMessage());
        }
    }

    /**
     * Show user permissions page.
     */
    public function permissions(User $user)
    {
        $permissions = Permission::all()->groupBy(function ($permission) {
            return explode('.', $permission->name)[0] ?? 'other';
        });
        
        $userPermissions = $user->getAllPermissions()->pluck('name')->toArray();
        
        return view('Auth::users.permissions', compact('user', 'permissions', 'userPermissions'));
    }

    /**
     * Assign permissions to user.
     */
    public function assignPermissions(Request $request, User $user)
    {
        $request->validate([
            'permissions' => 'nullable|array',
            'permissions.*' => 'exists:permissions,name',
        ]);

        try {
            $user->syncPermissions($request->permissions ?? []);
            
            return back()->with('success', 'تم تحديث صلاحيات المستخدم بنجاح');
        } catch (\Exception $e) {
            return back()->with('error', 'حدث خطأ أثناء تحديث الصلاحيات: ' . $e->getMessage());
        }
    }
}
