<?php

namespace Modules\Reservation\Providers;

use Illuminate\Support\ServiceProvider;
use Modules\Reservation\Contracts\ReservationServiceInterface;
use Modules\Reservation\Contracts\AreaServiceInterface;
use Modules\Reservation\Contracts\TableServiceInterface;
use Modules\Reservation\Contracts\QRCodeServiceInterface;
use Modules\Reservation\Contracts\WaiterRequestServiceInterface;
use Modules\Reservation\Services\ReservationService;
use Modules\Reservation\Services\AreaService;
use Modules\Reservation\Services\TableService;
use Modules\Reservation\Services\QRCodeService;
use Modules\Reservation\Services\WaiterRequestService;

class ReservationServiceProvider extends ServiceProvider
{
    /**
     * Register services.
     */
    public function register(): void
    {
        // Bind services
        $this->app->bind(ReservationServiceInterface::class, ReservationService::class);
        $this->app->bind(AreaServiceInterface::class, AreaService::class);
        $this->app->bind(TableServiceInterface::class, TableService::class);
        $this->app->bind(QRCodeServiceInterface::class, QRCodeService::class);
        $this->app->bind(WaiterRequestServiceInterface::class, WaiterRequestService::class);

        // Register singletons
        $this->app->singleton(ReservationService::class);
        $this->app->singleton(AreaService::class);
        $this->app->singleton(TableService::class);
        $this->app->singleton(QRCodeService::class);
        $this->app->singleton(WaiterRequestService::class);

        // // Merge configuration
        // $this->mergeConfigFrom(
        //     __DIR__ . '/../Config/reservation.php',
        //     'reservation'
        // );
    }

    /**
     * Bootstrap services.
     */
    public function boot(): void
    {
        

        // Load web routes
        $this->loadRoutesFrom(__DIR__ . '/../routes/web.php');
        $this->loadRoutesFrom(__DIR__ . '/../routes/api.php');


        // Load views
        $this->loadViewsFrom(__DIR__ . '/../resources/views', 'Reservation');

       
    }

    /**
     * Get the services provided by the provider.
     */
    public function provides(): array
    {
        return [
            ReservationServiceInterface::class,
            AreaServiceInterface::class,
            TableServiceInterface::class,
            QRCodeServiceInterface::class,
            WaiterRequestServiceInterface::class,
            ReservationService::class,
            AreaService::class,
            TableService::class,
            QRCodeService::class,
            WaiterRequestService::class,
        ];
    }
}