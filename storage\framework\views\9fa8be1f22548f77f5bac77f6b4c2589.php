<?php $__env->startPush('styles'); ?>
<meta name="csrf-token" content="<?php echo e(csrf_token()); ?>">
<!-- Material Design Icons -->
<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/@mdi/font@7.2.96/css/materialdesignicons.min.css">
<style>
:root {
    --primary: #2563eb;
    --primary-hover: #1d4ed8;
    --success: #059669;
    --success-hover: #047857;
    --warning: #d97706;
    --warning-hover: #b45309;
    --danger: #dc2626;
    --danger-hover: #b91c1c;
    --info: #0891b2;
    --info-hover: #0e7490;
    --gray-50: #f9fafb;
    --gray-100: #f3f4f6;
    --gray-200: #e5e7eb;
    --gray-300: #d1d5db;
    --gray-400: #9ca3af;
    --gray-500: #6b7280;
    --gray-600: #4b5563;
    --gray-700: #374151;
    --gray-800: #1f2937;
    --gray-900: #111827;
    --white: #ffffff;
}

.pos-dashboard {
    background-color: var(--gray-50);
    min-height: 100vh;
    padding: 2rem 1rem;
}

/* Header Section */
.dashboard-header {
    background-color: var(--white);
    border-radius: 16px;
    padding: 2rem;
    margin-bottom: 2rem;
    box-shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1);
    border: 1px solid var(--gray-200);
}

.header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 1rem;
}

.header-info h1 {
    font-size: 2rem;
    font-weight: 700;
    color: var(--gray-900);
    margin: 0 0 0.5rem 0;
}

.header-info p {
    color: var(--gray-600);
    margin: 0;
    font-size: 1rem;
}

.header-actions {
    display: flex;
    gap: 1rem;
    align-items: center;
}

.btn-primary-custom {
    background-color: var(--primary);
    color: var(--white);
    border: none;
    padding: 0.75rem 1.5rem;
    border-radius: 8px;
    font-weight: 600;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    transition: all 0.2s ease;
    font-size: 0.95rem;
}

.btn-primary-custom:hover {
    background-color: var(--primary-hover);
    color: var(--white);
    text-decoration: none;
    transform: translateY(-1px);
}

.btn-outline-custom {
    background-color: transparent;
    color: var(--gray-700);
    border: 1px solid var(--gray-300);
    padding: 0.75rem 1.5rem;
    border-radius: 8px;
    font-weight: 500;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    transition: all 0.2s ease;
    font-size: 0.95rem;
}

.btn-outline-custom:hover {
    background-color: var(--gray-100);
    color: var(--gray-800);
    text-decoration: none;
    border-color: var(--gray-400);
}

/* Stats Grid */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.stats-card {
    background-color: var(--white);
    border-radius: 12px;
    padding: 1.5rem;
    border: 1px solid var(--gray-200);
    transition: all 0.2s ease;
}

.stats-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1);
}

.stats-header {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    margin-bottom: 1rem;
}

.stats-icon {
    width: 48px;
    height: 48px;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 20px;
    color: var(--white);
}

.stats-icon.primary { background-color: var(--primary); }
.stats-icon.success { background-color: var(--success); }
.stats-icon.warning { background-color: var(--warning); }
.stats-icon.info { background-color: var(--info); }

.stats-label {
    font-size: 0.875rem;
    color: var(--gray-600);
    font-weight: 500;
}

.stats-value {
    font-size: 2rem;
    font-weight: 700;
    color: var(--gray-900);
    margin: 0;
}

/* Main Content Grid */
.content-grid {
    display: grid;
    grid-template-columns: 350px 1fr;
    gap: 2rem;
}

@media (max-width: 1024px) {
    .content-grid {
        grid-template-columns: 1fr;
    }
}

/* Quick Actions */
.quick-actions {
    background-color: var(--white);
    border-radius: 12px;
    padding: 1.5rem;
    border: 1px solid var(--gray-200);
    height: fit-content;
}

.section-title {
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--gray-900);
    margin: 0 0 1.5rem 0;
}

.action-item {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1rem;
    border-radius: 8px;
    text-decoration: none;
    color: var(--gray-700);
    transition: all 0.2s ease;
    margin-bottom: 0.5rem;
    border: 1px solid transparent;
}

.action-item:hover {
    background-color: var(--gray-50);
    color: var(--gray-900);
    text-decoration: none;
    border-color: var(--gray-200);
    transform: translateX(4px);
}

.action-icon {
    width: 40px;
    height: 40px;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 18px;
    color: var(--white);
    flex-shrink: 0;
}

.action-content h4 {
    font-size: 0.95rem;
    font-weight: 600;
    margin: 0 0 0.25rem 0;
    color: inherit;
}

.action-content p {
    font-size: 0.8rem;
    color: var(--gray-500);
    margin: 0;
}

/* Recent Orders */
.recent-orders {
    background-color: var(--white);
    border-radius: 12px;
    padding: 1.5rem;
    border: 1px solid var(--gray-200);
}

.section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;
}

.order-list {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.order-item {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1rem;
    border: 1px solid var(--gray-200);
    border-radius: 8px;
    transition: all 0.2s ease;
}

.order-item:hover {
    border-color: var(--gray-300);
    background-color: var(--gray-50);
}

.order-avatar {
    width: 48px;
    height: 48px;
    border-radius: 8px;
    background-color: var(--primary);
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--white);
    font-weight: 600;
    font-size: 0.875rem;
    flex-shrink: 0;
}

.order-details {
    flex: 1;
    min-width: 0;
}

.order-number {
    font-weight: 600;
    color: var(--gray-900);
    margin: 0 0 0.25rem 0;
    font-size: 0.95rem;
}

.order-meta {
    font-size: 0.8rem;
    color: var(--gray-500);
    margin: 0;
}

.order-actions {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    flex-shrink: 0;
}

.status-badge {
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.025em;
}

.status-pending {
    background-color: #fef3c7;
    color: #92400e;
}

.status-confirmed {
    background-color: #d1fae5;
    color: #065f46;
}

.status-preparing {
    background-color: #dbeafe;
    color: #1e40af;
}

.status-ready {
    background-color: #e0e7ff;
    color: #5b21b6;
}

.status-served {
    background-color: #f3e8ff;
    color: #6b21a8;
}

/* Payment Status Styles */
.payment-status {
    margin-top: 5px;
}

.payment-badge {
    display: inline-flex;
    align-items: center;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 600;
    gap: 4px;
}

.payment-due {
    background: #fef3c7;
    color: #92400e;
    border: 1px solid #fbbf24;
}

.payment-partially_paid {
    background: #dbeafe;
    color: #1e40af;
    border: 1px solid #60a5fa;
}

.payment-paid {
    background: #d1fae5;
    color: #065f46;
    border: 1px solid #34d399;
}

.btn-sm {
    padding: 0.5rem;
    border-radius: 6px;
    border: none;
    font-size: 0.875rem;
    cursor: pointer;
    transition: all 0.2s ease;
    display: inline-flex;
    align-items: center;
    justify-content: center;
}

.btn-success {
    background-color: var(--success);
    color: var(--white);
}

.btn-success:hover {
    background-color: var(--success-hover);
}

.btn-outline-primary {
    background-color: transparent;
    color: var(--primary);
    border: 1px solid var(--primary);
}

.btn-outline-primary:hover {
    background-color: var(--primary);
    color: var(--white);
}

/* Payment Modal Styles */
.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
}

.modal-content {
    background: white;
    border-radius: 12px;
    padding: 24px;
    width: 90%;
    max-width: 500px;
    max-height: 90vh;
    overflow-y: auto;
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding-bottom: 16px;
    border-bottom: 1px solid #e5e7eb;
}

.modal-title {
    font-size: 18px;
    font-weight: 600;
    color: #1f2937;
}

.modal-close {
    background: none;
    border: none;
    font-size: 24px;
    color: #6b7280;
    cursor: pointer;
    padding: 4px;
    border-radius: 4px;
    transition: all 0.2s;
}

.modal-close:hover {
    background: #f3f4f6;
    color: #374151;
}

.form-group {
    margin-bottom: 16px;
}

.form-label {
    display: block;
    font-size: 14px;
    font-weight: 500;
    color: #374151;
    margin-bottom: 6px;
}

.form-input, .form-select {
    width: 100%;
    padding: 10px 12px;
    border: 1px solid #d1d5db;
    border-radius: 6px;
    font-size: 14px;
    transition: all 0.2s;
}

.form-input:focus, .form-select:focus {
    outline: none;
    border-color: #3b82f6;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.payment-method-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: 12px;
    margin-bottom: 16px;
}

.payment-method-option {
    padding: 12px;
    border: 2px solid #e5e7eb;
    border-radius: 8px;
    text-align: center;
    cursor: pointer;
    transition: all 0.2s;
    background: white;
}

.payment-method-option:hover {
    border-color: #3b82f6;
    background: #f8fafc;
}

.payment-method-option.selected {
    border-color: #3b82f6;
    background: #eff6ff;
}

.payment-method-icon {
    font-size: 24px;
    margin-bottom: 8px;
    color: #6b7280;
}

.payment-method-option.selected .payment-method-icon {
    color: #3b82f6;
}

.payment-method-name {
    font-size: 12px;
    font-weight: 500;
    color: #374151;
}

.modal-actions {
    display: flex;
    gap: 12px;
    justify-content: flex-end;
    margin-top: 24px;
    padding-top: 16px;
    border-top: 1px solid #e5e7eb;
}

.btn-cancel {
    padding: 10px 20px;
    border: 1px solid #d1d5db;
    background: white;
    color: #374151;
    border-radius: 6px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s;
}

.btn-cancel:hover {
    background: #f9fafb;
    border-color: #9ca3af;
}

.btn-submit {
    padding: 10px 20px;
    background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
    color: white;
    border: none;
    border-radius: 6px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s;
}

.btn-submit:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
}

.btn-submit:disabled {
    background: #9ca3af;
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
}

/* Additional utility styles */
.p-3 {
    padding: 12px;
}

.bg-gray-50 {
    background-color: #f9fafb;
}

.rounded-lg {
    border-radius: 8px;
}

.text-gray-600 {
    color: #6b7280;
}

.flex {
    display: flex;
}

.justify-between {
    justify-content: space-between;
}

.items-center {
    align-items: center;
}

.mb-2 {
    margin-bottom: 8px;
}

.font-medium {
    font-weight: 500;
}

.font-bold {
    font-weight: 700;
}

.btn-warning {
    background-color: #f59e0b;
    color: white;
}

.btn-warning:hover {
    background-color: #d97706;
}

/* Empty State */
.empty-state {
    text-align: center;
    padding: 3rem 1rem;
}

.empty-icon {
    font-size: 4rem;
    color: var(--gray-400);
    margin-bottom: 1rem;
}

.empty-title {
    font-size: 1.125rem;
    font-weight: 600;
    color: var(--gray-700);
    margin: 0 0 0.5rem 0;
}

.empty-description {
    color: var(--gray-500);
    margin: 0 0 2rem 0;
}

/* Responsive Design */
@media (max-width: 768px) {
    .pos-dashboard {
        padding: 1rem 0.5rem;
    }
    
    .dashboard-header {
        padding: 1.5rem;
    }
    
    .header-content {
        flex-direction: column;
        align-items: flex-start;
    }
    
    .header-actions {
        width: 100%;
        justify-content: flex-start;
    }
    
    .stats-grid {
        grid-template-columns: 1fr;
        gap: 1rem;
    }
    
    .hero-title {
        font-size: 1.75rem;
    }
    
    .order-item {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.75rem;
    }
    
    .order-actions {
        width: 100%;
        justify-content: flex-end;
    }
}

/* Loading Animation */
.loading-spin {
    animation: spin 1s linear infinite;
}

@keyframes spin {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}
</style>
<?php $__env->stopPush(); ?>



<?php $__env->startSection('content'); ?>
<div class="pos-dashboard">
    <!-- Header Section -->
    <div class="dashboard-header">
        <div class="header-content">
            <div class="header-info">
                <h1>Point of Sale Dashboard</h1>
                <p>Manage your restaurant orders and track daily performance</p>
            </div>
            <div class="header-actions">
                <a href="<?php echo e(route('pos.create')); ?>" class="btn-primary-custom">
                    <i class="mdi mdi-plus"></i>
                    New Order
                </a>
                <a href="<?php echo e(route('orders.index')); ?>" class="btn-outline-custom">
                    <i class="mdi mdi-format-list-bulleted"></i>
                    View All Orders
                </a>
            </div>
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="stats-grid">
        <div class="stats-card">
            <div class="stats-header">
                <div class="stats-icon primary">
                    <i class="mdi mdi-receipt"></i>
                </div>
                <div class="stats-label">Today's Orders</div>
            </div>
            <div class="stats-value"><?php echo e($recentOrders->where('created_at', '>=', today())->count()); ?></div>
        </div>

        <div class="stats-card">
            <div class="stats-header">
                <div class="stats-icon warning">
                    <i class="mdi mdi-clock-outline"></i>
                </div>
                <div class="stats-label">Pending Orders</div>
            </div>
            <div class="stats-value"><?php echo e($recentOrders->where('status', 'pending')->count()); ?></div>
        </div>

        <div class="stats-card">
            <div class="stats-header">
                <div class="stats-icon info">
                    <i class="mdi mdi-chef-hat"></i>
                </div>
                <div class="stats-label">Preparing</div>
            </div>
            <div class="stats-value"><?php echo e($recentOrders->where('status', 'preparing')->count()); ?></div>
        </div>

        <div class="stats-card">
            <div class="stats-header">
                <div class="stats-icon success">
                    <i class="mdi mdi-currency-usd"></i>
                </div>
                <div class="stats-label">Today's Revenue</div>
            </div>
            <div class="stats-value">$<?php echo e(number_format($recentOrders->where('created_at', '>=', today())->sum('total_amount'), 0)); ?></div>
        </div>
    </div>

    <!-- Main Content -->
    <div class="content-grid">

        <!-- Recent Orders -->
        <div class="recent-orders">
            <div class="section-header">
                <h3 class="section-title">Recent Orders</h3>
                <a href="<?php echo e(route('orders.index')); ?>" class="btn-outline-custom">
                    <i class="mdi mdi-arrow-right"></i>
                    View All
                </a>
            </div>

            <?php if($recentOrders->count() > 0): ?>
                <div class="order-list">
                    <?php $__currentLoopData = $recentOrders; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $order): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <div class="order-item">
                        <div class="order-avatar">
                            #<?php echo e(substr($order->order_number, -3)); ?>

                        </div>
                        <div class="order-details">
                            <h4 class="order-number">#<?php echo e($order->order_number); ?></h4>
                            <p class="order-meta">
                                <?php echo e($order->customer ? $order->customer->first_name . ' ' . $order->customer->last_name : 'Walk-in Customer'); ?> •
                                <?php echo e($order->orderItems->count()); ?> items •
                                $<?php echo e(number_format($order->total_amount, 2)); ?> •
                                <?php echo e($order->created_at->format('h:i A')); ?>

                            </p>
                            <?php if($order->transaction): ?>
                                <p class="payment-status">
                                    <span class="payment-badge payment-<?php echo e($order->transaction->status); ?>">
                                        <?php if($order->transaction->status === 'paid'): ?>
                                            <i class="mdi mdi-check-circle"></i> Paid
                                        <?php elseif($order->transaction->status === 'partially_paid'): ?>
                                            <i class="mdi mdi-clock-outline"></i> Partially Paid ($<?php echo e(number_format($order->transaction->paid_amount, 2)); ?>)
                                        <?php else: ?>
                                            <i class="mdi mdi-currency-usd"></i> Due ($<?php echo e(number_format($order->transaction->due_amount, 2)); ?>)
                                        <?php endif; ?>
                                    </span>
                                </p>
                            <?php endif; ?>
                        </div>
                        <div class="order-actions">
                            <span class="status-badge status-<?php echo e($order->status); ?>">
                                <?php echo e(ucfirst($order->status)); ?>

                            </span>
                            <?php if($order->status === 'pending'): ?>
                                <button class="btn-sm btn-success confirm-order-btn" data-order-id="<?php echo e($order->id); ?>" title="Confirm Order">
                                    <i class="mdi mdi-check"></i>
                                </button>
                            <?php endif; ?>
                            <?php if($order->transaction && $order->transaction->status !== 'paid'): ?>
                                <button class="btn-sm btn-warning add-payment-btn" data-order-id="<?php echo e($order->id); ?>" data-transaction-id="<?php echo e($order->transaction->id); ?>" data-due-amount="<?php echo e($order->transaction->due_amount); ?>" title="Add Payment">
                                    <i class="mdi mdi-credit-card"></i>
                                </button>
                            <?php endif; ?>
                            <a href="<?php echo e(route('orders.show', $order->id)); ?>" class="btn-sm btn-outline-primary" title="View Details">
                                <i class="mdi mdi-eye"></i>
                            </a>
                        </div>
                    </div>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </div>
            <?php else: ?>
                <div class="empty-state">
                    <div class="empty-icon">
                        <i class="mdi mdi-receipt"></i>
                    </div>
                    <h3 class="empty-title">No Orders Yet</h3>
                    <p class="empty-description">Create your first order to get started</p>
                    <a href="<?php echo e(route('pos.create')); ?>" class="btn-primary-custom">
                        <i class="mdi mdi-plus"></i>
                        Create First Order
                    </a>
                </div>
            <?php endif; ?>
        </div>
    </div>
</div>

<!-- Payment Modal -->
<div id="paymentModal" class="modal-overlay" style="display: none;">
    <div class="modal-content">
        <div class="modal-header">
            <h3 class="modal-title">Add Payment</h3>
            <button type="button" class="modal-close" onclick="closePaymentModal()">
                <i class="mdi mdi-close"></i>
            </button>
        </div>

        <form id="paymentForm">
            <div class="form-group">
                <label class="form-label">Order Information</label>
                <div id="orderInfo" class="p-3 bg-gray-50 rounded-lg">
                    <!-- Order info will be populated here -->
                </div>
            </div>

            <div class="form-group">
                <label class="form-label">Payment Method</label>
                <div class="payment-method-grid" id="paymentMethodGrid">
                    <!-- Payment methods will be loaded here -->
                </div>
                <input type="hidden" id="selectedPaymentMethod" name="payment_method_id">
            </div>

            <div class="form-group">
                <label class="form-label">Payment Amount</label>
                <input type="number" id="paymentAmount" name="amount" class="form-input"
                       placeholder="0.00" step="0.01" min="0.01" required>
                <small class="text-gray-600">Due Amount: $<span id="dueAmount">0.00</span></small>
            </div>

            <div class="form-group">
                <label class="form-label">Payment Date</label>
                <input type="date" id="paymentDate" name="payment_date" class="form-input" required>
            </div>

            <div class="form-group">
                <label class="form-label">Notes (Optional)</label>
                <textarea id="paymentNotes" name="notes" class="form-input" rows="3"
                          placeholder="Payment notes..."></textarea>
            </div>

            <div class="modal-actions">
                <button type="button" class="btn-cancel" onclick="closePaymentModal()">Cancel</button>
                <button type="submit" class="btn-submit" id="submitPayment">Add Payment</button>
            </div>
        </form>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('scripts'); ?>
<!-- SweetAlert2 JS -->
<script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>

<script>
$(document).ready(function() {
    // Add CSRF token to all AJAX requests
    $.ajaxSetup({
        headers: {
            'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
        }
    });

    // Order confirmation functionality
    $('.confirm-order-btn').on('click', function() {
        const orderId = $(this).data('order-id');
        const button = $(this);
        const originalContent = button.html();
        
        // Show loading state
        button.prop('disabled', true).html('<i class="mdi mdi-loading loading-spin"></i>');
        
        $.ajax({
            url: `/api/orders/${orderId}/confirm`,
            method: 'POST',
            success: function(response) {
                if (response.success) {
                    // Extract order information
                    const orderNumber = response.data.order.order_number || orderId;
                    let kotNumber = null;
                    
                    // Check if KOT was created successfully
                    if (response.data.kot_orders && response.data.kot_orders.length > 0) {
                        kotNumber = response.data.kot_orders[0].kot_number || response.data.kot_orders[0].id;
                    }
                    
                    // Show success notification
                    showOrderConfirmationToast(orderNumber, kotNumber);
                    
                    // Update the order status display
                    const orderItem = button.closest('.order-item');
                    orderItem.find('.status-badge')
                        .removeClass('status-pending')
                        .addClass('status-confirmed')
                        .text('Confirmed');
                    
                    // Remove the confirm button
                    button.fadeOut();
                } else {
                    showOrderErrorToast(response.message || "Failed to confirm order");
                }
                
                // Restore button state
                button.prop('disabled', false).html(originalContent);
            },
            error: function(xhr) {
                console.error('Error confirming order:', xhr);
                let errorMessage = "Failed to confirm order";
                
                if (xhr.responseJSON && xhr.responseJSON.message) {
                    errorMessage = xhr.responseJSON.message;
                }
                
                showOrderErrorToast(errorMessage);
                
                // Restore button
                button.prop('disabled', false).html(originalContent);
            }
        });
    });

    // Toast notification functions (you'll need to implement these based on your notification system)
    function showOrderConfirmationToast(orderNumber, kotNumber) {
        // Implement your success toast notification here
        console.log('Order confirmed:', orderNumber, kotNumber);
    }

    function showOrderErrorToast(message) {
        // Implement your error toast notification here
        console.error('Order error:', message);
    }

    // Payment Modal Functionality
    let currentTransactionId = null;
    let currentOrderId = null;
    let paymentMethods = [];

    // Load payment methods on page load
    loadPaymentMethods();

    // Add payment button click handler
    $('.add-payment-btn').on('click', function() {
        currentOrderId = $(this).data('order-id');
        currentTransactionId = $(this).data('transaction-id');
        const dueAmount = $(this).data('due-amount');

        openPaymentModal(currentOrderId, currentTransactionId, dueAmount);
    });

    function loadPaymentMethods() {
        $.ajax({
            url: '/api/payments/payment-methods',
            method: 'GET',
            success: function(response) {
                if (response.success) {
                    paymentMethods = response.data;
                    renderPaymentMethods();
                }
            },
            error: function(xhr) {
                console.error('Failed to load payment methods:', xhr);
            }
        });
    }

    function renderPaymentMethods() {
        const grid = $('#paymentMethodGrid');
        grid.empty();

        paymentMethods.forEach(method => {
            const methodHtml = `
                <div class="payment-method-option" data-method-id="${method.id}">
                    <div class="payment-method-icon">
                        <i class="mdi mdi-${method.icon || 'credit-card'}"></i>
                    </div>
                    <div class="payment-method-name">${method.name}</div>
                </div>
            `;
            grid.append(methodHtml);
        });

        // Add click handlers for payment methods
        $('.payment-method-option').on('click', function() {
            $('.payment-method-option').removeClass('selected');
            $(this).addClass('selected');
            $('#selectedPaymentMethod').val($(this).data('method-id'));
        });
    }

    function openPaymentModal(orderId, transactionId, dueAmount) {
        // Set current date
        $('#paymentDate').val(new Date().toISOString().split('T')[0]);

        // Set due amount
        $('#dueAmount').text(dueAmount.toFixed(2));
        $('#paymentAmount').val(dueAmount);

        // Load order info
        loadOrderInfo(orderId);

        // Show modal
        $('#paymentModal').show();
    }

    function loadOrderInfo(orderId) {
        $.ajax({
            url: `/api/orders/${orderId}`,
            method: 'GET',
            success: function(response) {
                if (response.success) {
                    const order = response.data;
                    const orderInfoHtml = `
                        <div class="flex justify-between items-center mb-2">
                            <span class="font-medium">Order #${order.order_number}</span>
                            <span class="text-gray-600">${new Date(order.created_at).toLocaleDateString()}</span>
                        </div>
                        <div class="flex justify-between items-center mb-2">
                            <span>Customer:</span>
                            <span>${order.customer ? order.customer.first_name + ' ' + order.customer.last_name : 'Walk-in Customer'}</span>
                        </div>
                        <div class="flex justify-between items-center">
                            <span>Total Amount:</span>
                            <span class="font-bold">$${parseFloat(order.total_amount).toFixed(2)}</span>
                        </div>
                    `;
                    $('#orderInfo').html(orderInfoHtml);
                }
            },
            error: function(xhr) {
                console.error('Failed to load order info:', xhr);
            }
        });
    }

    // Payment form submission
    $('#paymentForm').on('submit', function(e) {
        e.preventDefault();

        const paymentMethodId = $('#selectedPaymentMethod').val();
        const amount = parseFloat($('#paymentAmount').val());
        const paymentDate = $('#paymentDate').val();
        const notes = $('#paymentNotes').val();

        if (!paymentMethodId) {
            Swal.fire('Error', 'Please select a payment method', 'error');
            return;
        }

        if (!amount || amount <= 0) {
            Swal.fire('Error', 'Please enter a valid payment amount', 'error');
            return;
        }

        // Disable submit button
        $('#submitPayment').prop('disabled', true).text('Processing...');

        // Submit payment
        $.ajax({
            url: '/api/payments',
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Accept': 'application/json'
            },
            data: JSON.stringify({
                transaction_id: currentTransactionId,
                payment_method_id: paymentMethodId,
                amount: amount,
                payment_date: paymentDate,
                notes: notes
            }),
            success: function(response) {
                $('#submitPayment').prop('disabled', false).text('Add Payment');

                if (response.success) {
                    Swal.fire({
                        icon: 'success',
                        title: 'Payment Added!',
                        text: 'Payment has been processed successfully',
                        confirmButtonText: 'OK'
                    }).then(() => {
                        closePaymentModal();
                        // Reload the page to update payment status
                        window.location.reload();
                    });
                } else {
                    Swal.fire('Error', response.message || 'Payment failed', 'error');
                }
            },
            error: function(xhr) {
                $('#submitPayment').prop('disabled', false).text('Add Payment');

                let errorMessage = 'Payment processing failed';
                if (xhr.responseJSON && xhr.responseJSON.message) {
                    errorMessage = xhr.responseJSON.message;
                }

                Swal.fire('Error', errorMessage, 'error');
            }
        });
    });
});

// Global functions for modal
function closePaymentModal() {
    $('#paymentModal').hide();
    $('#paymentForm')[0].reset();
    $('.payment-method-option').removeClass('selected');
    $('#selectedPaymentMethod').val('');
    currentTransactionId = null;
    currentOrderId = null;
}
</script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('layouts.master', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH D:\my dehive work\resturant-pos - Copy\epis - Copy\modules\Orders\Providers/../resources/views/pos/index.blade.php ENDPATH**/ ?>