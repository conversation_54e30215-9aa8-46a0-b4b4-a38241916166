<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Bill - Order #<?php echo e($order->order_number); ?></title>
    <style>
        body {
            font-family: 'Courier New', monospace;
            margin: 0;
            padding: 20px;
            font-size: 12px;
            line-height: 1.4;
        }
        .bill-container {
            max-width: 300px;
            margin: 0 auto;
            background: white;
        }
        .header {
            text-align: center;
            border-bottom: 2px solid #000;
            padding-bottom: 10px;
            margin-bottom: 15px;
        }
        .restaurant-name {
            font-size: 16px;
            font-weight: bold;
            margin-bottom: 5px;
        }
        .restaurant-info {
            font-size: 10px;
            margin-bottom: 2px;
        }
        .order-info {
            margin-bottom: 15px;
            border-bottom: 1px dashed #000;
            padding-bottom: 10px;
        }
        .order-info div {
            display: flex;
            justify-content: space-between;
            margin-bottom: 3px;
        }
        .items-section {
            margin-bottom: 15px;
        }
        .item {
            margin-bottom: 8px;
        }
        .item-header {
            display: flex;
            justify-content: space-between;
            font-weight: bold;
        }
        .item-details {
            font-size: 10px;
            color: #666;
            margin-left: 10px;
        }
        .totals-section {
            border-top: 1px dashed #000;
            padding-top: 10px;
            margin-bottom: 15px;
        }
        .total-line {
            display: flex;
            justify-content: space-between;
            margin-bottom: 3px;
        }
        .total-line.grand-total {
            font-weight: bold;
            font-size: 14px;
            border-top: 1px solid #000;
            padding-top: 5px;
            margin-top: 5px;
        }
        .footer {
            text-align: center;
            border-top: 2px solid #000;
            padding-top: 10px;
            font-size: 10px;
        }
        .payment-status {
            text-align: center;
            margin: 15px 0;
            padding: 8px;
            border: 2px solid #000;
            font-weight: bold;
        }
        .payment-status.due {
            background-color: #fff3cd;
            border-color: #ffc107;
        }
        .payment-status.paid {
            background-color: #d4edda;
            border-color: #28a745;
        }
        @media print {
            body { margin: 0; padding: 10px; }
            .bill-container { max-width: none; }
        }
    </style>
</head>
<body>
    <div class="bill-container">
        <!-- Header -->
        <div class="header">
            <div class="restaurant-name"><?php echo e($order->branch->name ?? 'Restaurant'); ?></div>
            <?php if($order->branch->address): ?>
                <div class="restaurant-info"><?php echo e($order->branch->address); ?></div>
            <?php endif; ?>
            <?php if($order->branch->phone): ?>
                <div class="restaurant-info">Tel: <?php echo e($order->branch->phone); ?></div>
            <?php endif; ?>
        </div>

        <!-- Order Information -->
        <div class="order-info">
            <div>
                <span>Order #:</span>
                <span><?php echo e($order->order_number); ?></span>
            </div>
            <div>
                <span>Date:</span>
                <span><?php echo e($order->created_at->format('d/m/Y H:i')); ?></span>
            </div>
            <?php if($order->table): ?>
                <div>
                    <span>Table:</span>
                    <span><?php echo e($order->table->name); ?></span>
                </div>
            <?php endif; ?>
            <?php if($order->customer): ?>
                <div>
                    <span>Customer:</span>
                    <span><?php echo e($order->customer->name); ?></span>
                </div>
            <?php endif; ?>
            <div>
                <span>Order Type:</span>
                <span><?php echo e(ucfirst(str_replace('_', ' ', $order->order_type))); ?></span>
            </div>
            <?php if($order->pax): ?>
                <div>
                    <span>Pax:</span>
                    <span><?php echo e($order->pax); ?></span>
                </div>
            <?php endif; ?>
        </div>

        <!-- Items -->
        <div class="items-section">
            <?php $__currentLoopData = $order->orderItems; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                <div class="item">
                    <div class="item-header">
                        <span><?php echo e($item->quantity); ?>x <?php echo e($item->menuItem->name); ?></span>
                        <span>$<?php echo e(number_format($item->total_price, 2)); ?></span>
                    </div>
                    <?php if($item->variant_name): ?>
                        <div class="item-details">Variant: <?php echo e($item->variant_name); ?></div>
                    <?php endif; ?>
                    <?php if($item->notes): ?>
                        <div class="item-details">Notes: <?php echo e($item->notes); ?></div>
                    <?php endif; ?>
                    <?php if($item->addons && count($item->addons) > 0): ?>
                        <?php $__currentLoopData = $item->addons; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $addon): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <div class="item-details">+ <?php echo e($addon['addon_name']); ?> (<?php echo e($addon['quantity']); ?>x) - $<?php echo e(number_format($addon['total_price'], 2)); ?></div>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    <?php endif; ?>
                </div>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
        </div>

        <!-- Totals -->
        <div class="totals-section">
            <div class="total-line">
                <span>Subtotal:</span>
                <span>$<?php echo e(number_format($order->subtotal, 2)); ?></span>
            </div>
            <?php if($order->discount_amount > 0): ?>
                <div class="total-line">
                    <span>Discount:</span>
                    <span>-$<?php echo e(number_format($order->discount_amount, 2)); ?></span>
                </div>
            <?php endif; ?>
            <?php if($order->tax_amount > 0): ?>
                <div class="total-line">
                    <span>Tax (10%):</span>
                    <span>$<?php echo e(number_format($order->tax_amount, 2)); ?></span>
                </div>
            <?php endif; ?>
            <?php if($order->service_charge > 0): ?>
                <div class="total-line">
                    <span>Service Charge:</span>
                    <span>$<?php echo e(number_format($order->service_charge, 2)); ?></span>
                </div>
            <?php endif; ?>
            <div class="total-line grand-total">
                <span>TOTAL:</span>
                <span>$<?php echo e(number_format($order->total_amount, 2)); ?></span>
            </div>
        </div>

        <!-- Payment Status -->
        <?php if($order->transaction): ?>
            <div class="payment-status <?php echo e($order->transaction->status === 'paid' ? 'paid' : 'due'); ?>">
                <?php if($order->transaction->status === 'paid'): ?>
                    PAID
                <?php elseif($order->transaction->status === 'partially_paid'): ?>
                    PARTIALLY PAID - Due: $<?php echo e(number_format($order->transaction->due_amount, 2)); ?>

                <?php else: ?>
                    AMOUNT DUE: $<?php echo e(number_format($order->transaction->due_amount, 2)); ?>

                <?php endif; ?>
            </div>
        <?php endif; ?>

        <?php if($order->notes): ?>
            <div style="margin: 15px 0; padding: 8px; border: 1px solid #ddd;">
                <strong>Notes:</strong> <?php echo e($order->notes); ?>

            </div>
        <?php endif; ?>

        <!-- Footer -->
        <div class="footer">
            <div>Thank you for your visit!</div>
            <div><?php echo e(now()->format('d/m/Y H:i:s')); ?></div>
        </div>
    </div>

    <script>
        // Auto print when loaded
        window.onload = function() {
            window.print();
        }
    </script>
</body>
</html>
<?php /**PATH D:\my dehive work\resturant-pos - Copy\epis - Copy\modules\Orders\Providers/../resources/views/pos/bill.blade.php ENDPATH**/ ?>