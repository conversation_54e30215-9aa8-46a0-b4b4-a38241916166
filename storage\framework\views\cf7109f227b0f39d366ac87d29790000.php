<?php $__env->startSection('title', 'إدارة عناصر القائمة'); ?>

<?php $__env->startSection('css'); ?>
<!-- DataTables Tailwind CSS CDN -->
<link href="https://cdn.datatables.net/1.13.6/css/dataTables.tailwindcss.min.css" rel="stylesheet">
<style>
    .menu-item-thumbnail {
        width: 50px;
        height: 50px;
        object-fit: cover;
        border-radius: 8px;
    }
    .menu-item-thumbnail-placeholder {
        width: 50px;
        height: 50px;
        background: #f3f4f6;
        border-radius: 8px;
        display: flex;
        align-items: center;
        justify-content: center;
        color: #9ca3af;
    }
    .dataTables_wrapper .dataTables_length,
    .dataTables_wrapper .dataTables_filter,
    .dataTables_wrapper .dataTables_info,
    .dataTables_wrapper .dataTables_paginate {
        margin: 0.5rem 0;
    }
</style>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('page-header'); ?>
<div class="bg-white shadow-sm border-b border-gray-200">
    <div class="px-4 sm:px-6 lg:px-8">
        <div class="py-6">
            <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between">
                <div>
                    <h1 class="text-2xl font-bold text-gray-900">إدارة عناصر القائمة</h1>
                    <p class="mt-1 text-sm text-gray-500">إدارة وتنظيم عناصر القائمة في المطعم</p>
                </div>
                <div class="mt-4 sm:mt-0">
                    <button type="button" id="add-menu-item-btn" class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors duration-200">
                        <i class="fas fa-plus mr-2"></i>
                        إضافة عنصر جديد
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('content'); ?>
<div class="space-y-6">
    <!-- Filters Section -->
    <div class="bg-white shadow-sm rounded-lg border border-gray-200">
        <div class="px-6 py-4 border-b border-gray-200">
            <h3 class="text-lg font-medium text-gray-900">فلاتر البحث</h3>
        </div>
        <div class="p-6">
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                <div>
                    <label for="filter-menu" class="block text-sm font-medium text-gray-700 mb-2">القائمة</label>
                    <select id="filter-menu" class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500">
                        <option value="">جميع القوائم</option>
                    </select>
                </div>
                <div>
                    <label for="filter-category" class="block text-sm font-medium text-gray-700 mb-2">الفئة</label>
                    <select id="filter-category" class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500">
                        <option value="">جميع الفئات</option>
                    </select>
                </div>
                <div>
                    <label for="filter-status" class="block text-sm font-medium text-gray-700 mb-2">الحالة</label>
                    <select id="filter-status" class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500">
                        <option value="">جميع الحالات</option>
                        <option value="active">نشط</option>
                        <option value="inactive">غير نشط</option>
                    </select>
                </div>
                <div>
                    <label for="filter-featured" class="block text-sm font-medium text-gray-700 mb-2">مميز</label>
                    <select id="filter-featured" class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500">
                        <option value="">الكل</option>
                        <option value="yes">مميز</option>
                        <option value="no">غير مميز</option>
                    </select>
                </div>
            </div>
            <div class="mt-4 flex flex-wrap gap-2">
                <button type="button" id="apply-filters" class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                    <i class="fas fa-search mr-2"></i>
                    تطبيق الفلاتر
                </button>
                <button type="button" id="clear-filters" class="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                    <i class="fas fa-times mr-2"></i>
                    مسح الفلاتر
                </button>
            </div>
        </div>
    </div>

    <!-- DataTable Section -->
    <div class="bg-white shadow-sm rounded-lg border border-gray-200">
        <div class="px-6 py-4 border-b border-gray-200">
            <h3 class="text-lg font-medium text-gray-900">قائمة عناصر القائمة</h3>
        </div>
        <div class="p-6">
            <div class="overflow-x-auto">
                <table id="menu-items-table" class="w-full table-auto">
                    <thead>
                        <tr class="bg-gray-50">
                            <th class="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">#</th>
                            <th class="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">الصورة</th>
                            <th class="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">الاسم</th>
                            <th class="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">الكود</th>
                            <th class="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">القائمة</th>
                            <th class="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">الفئة</th>
                            <th class="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">السعر</th>
                            <th class="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">الحالة</th>
                            <th class="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">مميز</th>
                            <th class="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">تاريخ الإنشاء</th>
                            <th class="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        <!-- DataTable will populate this -->
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

<!-- Add/Edit Menu Item Modal -->
<div id="menu-item-modal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50 hidden">
    <div class="relative top-20 mx-auto p-5 border w-11/12 md:w-3/4 lg:w-1/2 shadow-lg rounded-md bg-white">
        <div class="mt-3">
            <!-- Modal Header -->
            <div class="flex items-center justify-between pb-4 border-b border-gray-200">
                <h3 class="text-lg font-medium text-gray-900" id="modal-title">إضافة عنصر قائمة جديد</h3>
                <button type="button" class="modal-close text-gray-400 hover:text-gray-600 transition-colors duration-200">
                    <i class="fas fa-times text-xl"></i>
                </button>
            </div>

            <!-- Modal Body -->
            <div class="mt-6">
                <form id="menu-item-form" enctype="multipart/form-data">
                    <input type="hidden" id="menu-item-id" name="id">
                    <input type="hidden" name="_token" value="<?php echo e(csrf_token()); ?>">
                    
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <!-- Basic Information -->
                        <div class="space-y-4">
                            <h4 class="text-md font-medium text-gray-900 border-b pb-2">المعلومات الأساسية</h4>
                            
                            <div>
                                <label for="menu_id" class="block text-sm font-medium text-gray-700 mb-2">القائمة *</label>
                                <select id="menu_id" name="menu_id" required class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500">
                                    <option value="">اختر القائمة</option>
                                </select>
                                <div class="text-red-500 text-sm mt-1 hidden" id="menu_id-error"></div>
                            </div>

                            <div>
                                <label for="category_id" class="block text-sm font-medium text-gray-700 mb-2">الفئة</label>
                                <select id="category_id" name="category_id" class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500">
                                    <option value="">اختر الفئة</option>
                                </select>
                                <div class="text-red-500 text-sm mt-1 hidden" id="category_id-error"></div>
                            </div>

                            <div>
                                <label for="name" class="block text-sm font-medium text-gray-700 mb-2">اسم العنصر *</label>
                                <input type="text" id="name" name="name" required class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500" placeholder="أدخل اسم العنصر">
                                <div class="text-red-500 text-sm mt-1 hidden" id="name-error"></div>
                            </div>

                            <div>
                                <label for="code" class="block text-sm font-medium text-gray-700 mb-2">كود العنصر *</label>
                                <input type="text" id="code" name="code" required class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500" placeholder="أدخل كود العنصر">
                                <div class="text-red-500 text-sm mt-1 hidden" id="code-error"></div>
                            </div>

                            <div>
                                <label for="description" class="block text-sm font-medium text-gray-700 mb-2">الوصف</label>
                                <textarea id="description" name="description" rows="3" class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500" placeholder="أدخل وصف العنصر"></textarea>
                                <div class="text-red-500 text-sm mt-1 hidden" id="description-error"></div>
                            </div>
                        </div>

                        <!-- Pricing and Details -->
                        <div class="space-y-4">
                            <h4 class="text-md font-medium text-gray-900 border-b pb-2">التسعير والتفاصيل</h4>
                            
                            <div>
                                <label for="base_price" class="block text-sm font-medium text-gray-700 mb-2">السعر الأساسي *</label>
                                <input type="number" id="base_price" name="base_price" step="0.01" min="0" required class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500" placeholder="0.00">
                                <div class="text-red-500 text-sm mt-1 hidden" id="base_price-error"></div>
                            </div>

                            <div>
                                <label for="cost_price" class="block text-sm font-medium text-gray-700 mb-2">سعر التكلفة</label>
                                <input type="number" id="cost_price" name="cost_price" step="0.01" min="0" class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500" placeholder="0.00">
                                <div class="text-red-500 text-sm mt-1 hidden" id="cost_price-error"></div>
                            </div>

                            <div>
                                <label for="prep_time_minutes" class="block text-sm font-medium text-gray-700 mb-2">وقت التحضير (دقيقة)</label>
                                <input type="number" id="prep_time_minutes" name="prep_time_minutes" min="0" class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500" placeholder="0">
                                <div class="text-red-500 text-sm mt-1 hidden" id="prep_time_minutes-error"></div>
                            </div>

                            <div>
                                <label for="calories" class="block text-sm font-medium text-gray-700 mb-2">السعرات الحرارية</label>
                                <input type="number" id="calories" name="calories" min="0" class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500" placeholder="0">
                                <div class="text-red-500 text-sm mt-1 hidden" id="calories-error"></div>
                            </div>

                            <div class="grid grid-cols-2 gap-4">
                                <div>
                                    <label class="flex items-center">
                                        <input type="checkbox" id="is_active" name="is_active" value="1" checked class="rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50">
                                        <span class="mr-2 text-sm text-gray-700">نشط</span>
                                    </label>
                                </div>
                                <div>
                                    <label class="flex items-center">
                                        <input type="checkbox" id="is_featured" name="is_featured" value="1" class="rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50">
                                        <span class="mr-2 text-sm text-gray-700">مميز</span>
                                    </label>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Modal Footer -->
                    <div class="mt-8 flex justify-end space-x-3 pt-4 border-t border-gray-200">
                        <button type="button" class="modal-close px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                            إلغاء
                        </button>
                        <button type="submit" id="submit-btn" class="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                            <i class="fas fa-save mr-2"></i>
                            حفظ
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- View Menu Item Modal -->
<div id="view-menu-item-modal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50 hidden">
    <div class="relative top-20 mx-auto p-5 border w-11/12 md:w-3/4 lg:w-1/2 shadow-lg rounded-md bg-white">
        <div class="mt-3">
            <!-- Modal Header -->
            <div class="flex items-center justify-between pb-4 border-b border-gray-200">
                <h3 class="text-lg font-medium text-gray-900">تفاصيل عنصر القائمة</h3>
                <button type="button" class="modal-close text-gray-400 hover:text-gray-600 transition-colors duration-200">
                    <i class="fas fa-times text-xl"></i>
                </button>
            </div>

            <!-- Modal Body -->
            <div class="mt-6" id="view-menu-item-content">
                <!-- Content will be loaded here -->
            </div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('scripts'); ?>
<!-- jQuery -->
<script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
<!-- DataTables JS -->
<script src="https://cdn.datatables.net/1.13.6/js/jquery.dataTables.min.js"></script>
<script src="https://cdn.datatables.net/1.13.6/js/dataTables.tailwindcss.min.js"></script>
<!-- SweetAlert -->
<script src="https://unpkg.com/sweetalert/dist/sweetalert.min.js"></script>

<script>
$(document).ready(function() {
    let table;
    let isEditMode = false;

    // Initialize DataTable
    function initDataTable() {
        table = $('#menu-items-table').DataTable({
            processing: true,
            serverSide: true,
            ajax: {
                url: "<?php echo e(route('menu-items.index')); ?>",
                data: function(d) {
                    d.menu_id = $('#filter-menu').val();
                    d.category_id = $('#filter-category').val();
                    d.status = $('#filter-status').val();
                    d.featured = $('#filter-featured').val();
                }
            },
            columns: [
                {data: 'DT_RowIndex', name: 'DT_RowIndex', orderable: false, searchable: false},
                {data: 'image', name: 'image', orderable: false, searchable: false},
                {data: 'name', name: 'name'},
                {data: 'code', name: 'code'},
                {data: 'menu_name', name: 'menu.name'},
                {data: 'category_name', name: 'category.name'},
                {data: 'base_price', name: 'base_price'},
                {data: 'is_active', name: 'is_active'},
                {data: 'is_featured', name: 'is_featured'},
                {data: 'created_at', name: 'created_at'},
                {data: 'action', name: 'action', orderable: false, searchable: false}
            ],
            order: [[9, 'desc']],
            pageLength: 25,
            responsive: true,
            language: {
                url: '//cdn.datatables.net/plug-ins/1.13.6/i18n/ar.json'
            }
        });
    }

    // Load filter options
    function loadFilterOptions() {
        // Load menus
        $.get("<?php echo e(route('menu-items.menus-list')); ?>", function(response) {
            if (response.success) {
                $('#filter-menu, #menu_id').empty().append('<option value="">اختر القائمة</option>');
                response.data.forEach(function(menu) {
                    $('#filter-menu').append(`<option value="${menu.id}">${menu.name}</option>`);
                    $('#menu_id').append(`<option value="${menu.id}">${menu.name}</option>`);
                });
            }
        });

        // Load categories
        $.get("<?php echo e(route('menu-items.categories-list')); ?>", function(response) {
            if (response.success) {
                $('#filter-category, #category_id').empty().append('<option value="">اختر الفئة</option>');
                response.data.forEach(function(category) {
                    $('#filter-category').append(`<option value="${category.id}">${category.name}</option>`);
                    $('#category_id').append(`<option value="${category.id}">${category.name}</option>`);
                });
            }
        });
    }

    // Initialize
    initDataTable();
    loadFilterOptions();

    // Filter functionality
    $('#apply-filters').on('click', function() {
        table.ajax.reload();
    });

    $('#clear-filters').on('click', function() {
        $('#filter-menu, #filter-category, #filter-status, #filter-featured').val('');
        table.ajax.reload();
    });

    // Modal functionality
    function openModal(modalId) {
        $(`#${modalId}`).removeClass('hidden');
        $('body').addClass('overflow-hidden');
    }

    function closeModal(modalId) {
        $(`#${modalId}`).addClass('hidden');
        $('body').removeClass('overflow-hidden');
    }

    // Close modal events
    $('.modal-close').on('click', function() {
        closeModal('menu-item-modal');
        closeModal('view-menu-item-modal');
        resetForm();
    });

    // Close modal when clicking outside
    $('#menu-item-modal, #view-menu-item-modal').on('click', function(e) {
        if (e.target === this) {
            closeModal('menu-item-modal');
            closeModal('view-menu-item-modal');
            resetForm();
        }
    });

    // Add new menu item
    $('#add-menu-item-btn').on('click', function() {
        isEditMode = false;
        $('#modal-title').text('إضافة عنصر قائمة جديد');
        $('#submit-btn').html('<i class="fas fa-save mr-2"></i>حفظ');
        resetForm();
        openModal('menu-item-modal');
    });

    // Edit menu item
    $(document).on('click', '.edit-item', function() {
        const id = $(this).data('id');
        isEditMode = true;
        $('#modal-title').text('تعديل عنصر القائمة');
        $('#submit-btn').html('<i class="fas fa-save mr-2"></i>تحديث');
        
        $.get(`<?php echo e(url('menu/menu-items')); ?>/${id}/edit`, function(response) {
            if (response.success) {
                const item = response.data;
                $('#menu-item-id').val(item.id);
                $('#menu_id').val(item.menu_id);
                $('#category_id').val(item.category_id);
                $('#name').val(item.name);
                $('#code').val(item.code);
                $('#description').val(item.description);
                $('#base_price').val(item.base_price);
                $('#cost_price').val(item.cost_price);
                $('#prep_time_minutes').val(item.prep_time_minutes);
                $('#calories').val(item.calories);
                $('#is_active').prop('checked', item.is_active);
                $('#is_featured').prop('checked', item.is_featured);
                
                openModal('menu-item-modal');
            } else {
                swal('خطأ!', response.message, 'error');
            }
        });
    });

    // View menu item
    $(document).on('click', '.show-item', function() {
        const id = $(this).data('id');
        
        $.get(`<?php echo e(url('menu/menu-items')); ?>/${id}/show`, function(response) {
            if (response.success) {
                const item = response.data;
                let content = `
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div class="space-y-4">
                            <h4 class="text-md font-medium text-gray-900 border-b pb-2">المعلومات الأساسية</h4>
                            <div><strong>الاسم:</strong> ${item.name}</div>
                            <div><strong>الكود:</strong> ${item.code}</div>
                            <div><strong>القائمة:</strong> ${item.menu ? item.menu.name : '-'}</div>
                            <div><strong>الفئة:</strong> ${item.category ? item.category.name : '-'}</div>
                            <div><strong>الوصف:</strong> ${item.description || '-'}</div>
                        </div>
                        <div class="space-y-4">
                            <h4 class="text-md font-medium text-gray-900 border-b pb-2">التسعير والتفاصيل</h4>
                            <div><strong>السعر الأساسي:</strong> ${item.base_price} ر.س</div>
                            <div><strong>سعر التكلفة:</strong> ${item.cost_price || '-'} ر.س</div>
                            <div><strong>وقت التحضير:</strong> ${item.prep_time_minutes || '-'} دقيقة</div>
                            <div><strong>السعرات الحرارية:</strong> ${item.calories || '-'}</div>
                            <div><strong>الحالة:</strong> ${item.is_active ? 'نشط' : 'غير نشط'}</div>
                            <div><strong>مميز:</strong> ${item.is_featured ? 'نعم' : 'لا'}</div>
                        </div>
                    </div>
                `;
                $('#view-menu-item-content').html(content);
                openModal('view-menu-item-modal');
            } else {
                swal('خطأ!', response.message, 'error');
            }
        });
    });

    // Delete menu item
    $(document).on('click', '.delete-item', function() {
        const id = $(this).data('id');
        
        swal({
            title: 'هل أنت متأكد؟',
            text: 'لن تتمكن من التراجع عن هذا الإجراء!',
            icon: 'warning',
            buttons: {
                cancel: {
                    text: 'إلغاء',
                    value: null,
                    visible: true,
                    className: 'btn btn-secondary'
                },
                confirm: {
                    text: 'نعم، احذف!',
                    value: true,
                    visible: true,
                    className: 'btn btn-danger'
                }
            }
        }).then((willDelete) => {
            if (willDelete) {
                $.ajax({
                    url: `<?php echo e(url('menu/menu-items')); ?>/${id}`,
                    type: 'DELETE',
                    data: {
                        _token: '<?php echo e(csrf_token()); ?>'
                    },
                    success: function(response) {
                        if (response.success) {
                            swal('تم الحذف!', response.message, 'success');
                            table.ajax.reload();
                        } else {
                            swal('خطأ!', response.message, 'error');
                        }
                    },
                    error: function() {
                        swal('خطأ!', 'حدث خطأ أثناء حذف العنصر', 'error');
                    }
                });
            }
        });
    });

    // Form submission
    $('#menu-item-form').on('submit', function(e) {
        e.preventDefault();
        
        const formData = new FormData(this);
        const url = isEditMode ? `<?php echo e(url('menu/menu-items')); ?>/${$('#menu-item-id').val()}` : '<?php echo e(route('menu-items.store')); ?>';
        const method = isEditMode ? 'PUT' : 'POST';
        
        if (isEditMode) {
            formData.append('_method', 'PUT');
        }

        // Clear previous errors
        $('.text-red-500').addClass('hidden');
        
        $.ajax({
            url: url,
            type: 'POST',
            data: formData,
            processData: false,
            contentType: false,
            success: function(response) {
                if (response.success) {
                    swal('نجح!', response.message, 'success');
                    closeModal('menu-item-modal');
                    resetForm();
                    table.ajax.reload();
                } else {
                    swal('خطأ!', response.message, 'error');
                }
            },
            error: function(xhr) {
                if (xhr.status === 422) {
                    const errors = xhr.responseJSON.errors;
                    Object.keys(errors).forEach(function(key) {
                        $(`#${key}-error`).text(errors[key][0]).removeClass('hidden');
                    });
                } else {
                    swal('خطأ!', 'حدث خطأ أثناء حفظ البيانات', 'error');
                }
            }
        });
    });

    // Reset form
    function resetForm() {
        $('#menu-item-form')[0].reset();
        $('#menu-item-id').val('');
        $('.text-red-500').addClass('hidden');
        $('#is_active').prop('checked', true);
        $('#is_featured').prop('checked', false);
    }
});
</script>
<?php $__env->stopPush(); ?>
<?php echo $__env->make('layouts.master', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH D:\my dehive work\resturant-pos - Copy\epis - Copy\modules\Menu\Providers/../resources/views/menu-items.blade.php ENDPATH**/ ?>