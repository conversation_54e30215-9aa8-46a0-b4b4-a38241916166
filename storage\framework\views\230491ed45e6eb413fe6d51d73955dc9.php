<?php $__env->startPush('styles'); ?>
<meta name="csrf-token" content="<?php echo e(csrf_token()); ?>">
<!-- DataTables CSS -->
<link rel="stylesheet" href="https://cdn.datatables.net/1.13.7/css/jquery.dataTables.min.css">
<link rel="stylesheet" href="https://cdn.datatables.net/buttons/2.4.2/css/buttons.dataTables.min.css">
<link rel="stylesheet" href="https://cdn.datatables.net/responsive/2.5.0/css/responsive.dataTables.min.css">
<!-- Select2 CSS -->
<link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet" />
<style>
    /* Custom DataTables Tailwind Styling */
    .dataTables_wrapper {
        font-family: inherit;
    }
    
    .dataTables_wrapper .dataTables_length,
    .dataTables_wrapper .dataTables_filter,
    .dataTables_wrapper .dataTables_info,
    .dataTables_wrapper .dataTables_processing,
    .dataTables_wrapper .dataTables_paginate {
        color: #374151;
        font-size: 0.875rem;
        padding: 0.5rem 0.75rem;
    }
    .dataTables_processing{
        margin: auto
    }
    .dataTables_wrapper .dataTables_length select,
    .dataTables_wrapper .dataTables_filter input {
        border: 1px solid #d1d5db;
        border-radius: 0.375rem;
        padding: 0.375rem 0.75rem;
        font-size: 0.875rem;
    }
    
    .dataTables_wrapper .dataTables_filter input:focus {
        outline: none;
        border-color: #3b82f6;
        box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
    }
    
    .dataTables_wrapper .dataTables_paginate .paginate_button {
        padding: 0.5rem 0.75rem !important;
        margin: 0 0.125rem !important;
        border-radius: 0.375rem !important;
        border: 1px solid #d1d5db !important;
        background: white !important;
        color: #374151 !important;
        text-decoration: none !important;
    }
    
    .dataTables_wrapper .dataTables_paginate .paginate_button:hover {
        background: #f3f4f6 !important;
        border-color: #9ca3af !important;
        color: #374151 !important;
    }
    
    .dataTables_wrapper .dataTables_paginate .paginate_button.current {
        background: #3b82f6 !important;
        border-color: #3b82f6 !important;
        color: white !important;
    }
    
    .dataTables_wrapper .dataTables_paginate .paginate_button.disabled {
        background: #f9fafb !important;
        color: #9ca3af !important;
        border-color: #e5e7eb !important;
    }
    
    table.dataTable {
        border-collapse: separate !important;
        border-spacing: 0 !important;
    }
    
    table.dataTable thead th {
        background-color: #f9fafb;
        border-bottom: 1px solid #e5e7eb;
        font-weight: 600;
        color: #374151;
        padding: 0.75rem;
    }
    
    table.dataTable tbody td {
        padding: 0.75rem;
        border-bottom: 1px solid #f3f4f6;
    }
    
    table.dataTable tbody tr:hover {
        background-color: #f9fafb;
    }
    
    .select2-container--default .select2-selection--single {
        height: 42px;
        border: 1px solid #d1d5db;
        border-radius: 0.5rem;
        padding: 0.5rem;
    }
    
    .select2-container--default .select2-selection--single .select2-selection__rendered {
        line-height: 26px;
        padding-left: 0;
    }
    
    .select2-container--default .select2-selection--single .select2-selection__arrow {
        height: 40px;
    }
    
    .modal-backdrop {
        background-color: rgba(0, 0, 0, 0.5);
    }
    
    .animate-fadeIn {
        animation: fadeIn 0.3s ease-in-out;
    }
    
    @keyframes fadeIn {
        from { opacity: 0; transform: scale(0.95); }
        to { opacity: 1; transform: scale(1); }
    }
    
    .animate-slideDown {
        animation: slideDown 0.3s ease-in-out;
    }
    
    @keyframes slideDown {
        from { opacity: 0; transform: translateY(-10px); }
        to { opacity: 1; transform: translateY(0); }
    }
</style>
<?php $__env->stopPush(); ?>
<?php $__env->startSection('page-header'); ?>
<div class="mb-6">
    <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between">
        <div class="mb-4 sm:mb-0">
            <nav class="flex mb-2" aria-label="Breadcrumb">
                <ol class="inline-flex items-center space-x-1 md:space-x-3">
                    <li class="inline-flex items-center">
                        <span class="text-gray-700 text-sm font-medium">إدارة النظام</span>
                    </li>
                    <li>
                        <div class="flex items-center">
                            <i class="fas fa-chevron-left text-gray-400 mx-2"></i>
                            <span class="text-gray-500 text-sm">الصلاحيات</span>
                        </div>
                    </li>
                </ol>
            </nav>
            <h1 class="text-2xl font-semibold text-gray-900">إدارة الصلاحيات</h1>
        </div>
        <div class="flex space-x-2">
            <button type="button" class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500" onclick="openModal('addPermissionModal')">
                <i class="fas fa-plus -ml-1 mr-2 h-4 w-4"></i>
                إضافة صلاحية
            </button>
            <button type="button" class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-yellow-600 hover:bg-yellow-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-yellow-500" onclick="openModal('bulkCreateModal')">
                <i class="fas fa-plus-square -ml-1 mr-2 h-4 w-4"></i>
                إنشاء مجمع
            </button>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('content'); ?>
<div class="bg-white shadow rounded-lg">
    <div class="px-6 py-4 border-b border-gray-200">
        <div class="flex justify-between items-center">
            <div>
                <h3 class="text-lg font-medium text-gray-900">قائمة الصلاحيات</h3>
                <p class="mt-1 text-sm text-gray-500">إدارة صلاحيات النظام وتجميعها حسب الوحدات</p>
            </div>
            <div class="flex items-center">
                <i class="fas fa-ellipsis-h text-gray-400"></i>
            </div>
        </div>
    </div>

    <div class="p-6">
        <!-- Filters -->
        <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
            <div>
                <label for="groupFilter" class="block text-sm font-medium text-gray-700 mb-2">تصفية حسب المجموعة:</label>
                <select id="groupFilter" class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                    <option value="">جميع المجموعات</option>
                    <?php $__currentLoopData = $groups; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $group): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <option value="<?php echo e($group); ?>"><?php echo e(ucfirst($group)); ?></option>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </select>
            </div>
            <div>
                <label for="guardFilter" class="block text-sm font-medium text-gray-700 mb-2">تصفية حسب الحارس:</label>
                <select id="guardFilter" class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                    <option value="">جميع الحراس</option>
                    <option value="web">ويب</option>
                    <option value="api">API</option>
                    <option value="sanctum">Sanctum</option>
                </select>
            </div>
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">&nbsp;</label>
                <div class="flex space-x-2">
                    <button type="button" class="px-4 py-2 bg-blue-600 text-white text-sm font-medium rounded-md hover:bg-blue-700" onclick="applyFilters()">تطبيق التصفية</button>
                    <button type="button" class="px-4 py-2 bg-gray-500 text-white text-sm font-medium rounded-md hover:bg-gray-600" onclick="clearFilters()">مسح التصفية</button>
                </div>
            </div>
        </div>

      
        </div>

        <!-- DataTable Container -->
        <div class="bg-white shadow-sm rounded-lg border border-gray-200 overflow-hidden">
            <div class="overflow-x-auto">
                <table id="permissionsTable" class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th class="px-6 py-4 text-right text-xs font-medium text-gray-500 uppercase tracking-wider border-b border-gray-200">#</th>
                            <th class="px-6 py-4 text-right text-xs font-medium text-gray-500 uppercase tracking-wider border-b border-gray-200">اسم الصلاحية</th>
                            <th class="px-6 py-4 text-right text-xs font-medium text-gray-500 uppercase tracking-wider border-b border-gray-200">المجموعة</th>
                            <th class="px-6 py-4 text-right text-xs font-medium text-gray-500 uppercase tracking-wider border-b border-gray-200">الحارس</th>
                            <th class="px-6 py-4 text-right text-xs font-medium text-gray-500 uppercase tracking-wider border-b border-gray-200">عدد الأدوار</th>
                            <th class="px-6 py-4 text-right text-xs font-medium text-gray-500 uppercase tracking-wider border-b border-gray-200">تاريخ الإنشاء</th>
                            <th class="px-6 py-4 text-right text-xs font-medium text-gray-500 uppercase tracking-wider border-b border-gray-200">الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                    </tbody>
                </table>
            </div>

            <!-- DataTable Info and Pagination -->
            <div class="bg-gray-50 px-6 py-3 border-t border-gray-200">
                <div class="flex items-center justify-between">
                    <div class="text-sm text-gray-700" id="tableInfo">
                        <!-- DataTable info will be inserted here -->
                    </div>
                    <div id="tablePagination">
                        <!-- DataTable pagination will be inserted here -->
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Add Permission Modal -->
<div id="addPermissionModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden z-50">
    <div class="relative top-20 mx-auto p-5 border w-11/12 max-w-md shadow-lg rounded-md bg-white">
        <div class="flex justify-between items-center pb-3">
            <h3 class="text-lg font-medium text-gray-900">إضافة صلاحية جديدة</h3>
            <button type="button" class="text-gray-400 hover:text-gray-600" onclick="closeModal('addPermissionModal')">
                <i class="fas fa-times"></i>
            </button>
        </div>
        <form id="addPermissionForm" action="<?php echo e(route('permissions.store')); ?>" method="POST">
            <?php echo csrf_field(); ?>
            <div class="mt-4">
                <div class="mb-4">
                    <label for="name" class="block text-sm font-medium text-gray-700 mb-2">اسم الصلاحية <span class="text-red-500">*</span></label>
                    <input type="text" class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500" id="name" name="name" required>
                    <p class="mt-1 text-sm text-gray-500">مثال: users.create, orders.view</p>
                </div>
                <div class="mb-4">
                    <label for="guard_name" class="block text-sm font-medium text-gray-700 mb-2">الحارس <span class="text-red-500">*</span></label>
                    <select class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500" id="guard_name" name="guard_name" required>
                        <option value="web">ويب</option>
                        <option value="api">API</option>
                        <option value="sanctum">Sanctum</option>
                    </select>
                </div>
            </div>
            <div class="flex justify-end space-x-2 pt-4">
                <button type="button" class="px-4 py-2 bg-gray-500 text-white text-sm font-medium rounded-md hover:bg-gray-600" onclick="closeModal('addPermissionModal')">إلغاء</button>
                <button type="submit" class="px-4 py-2 bg-blue-600 text-white text-sm font-medium rounded-md hover:bg-blue-700">حفظ</button>
            </div>
        </form>
    </div>
</div>

<!-- Edit Permission Modal -->
<div id="editPermissionModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden z-50">
    <div class="relative top-20 mx-auto p-5 border w-11/12 max-w-md shadow-lg rounded-md bg-white">
        <div class="flex justify-between items-center pb-3">
            <h3 class="text-lg font-medium text-gray-900">تعديل الصلاحية</h3>
            <button type="button" class="text-gray-400 hover:text-gray-600" onclick="closeModal('editPermissionModal')">
                <i class="fas fa-times"></i>
            </button>
        </div>
        <form id="editPermissionForm" method="POST">
            <?php echo csrf_field(); ?>
            <?php echo method_field('PUT'); ?>
            <div class="mt-4">
                <div class="mb-4">
                    <label for="edit_name" class="block text-sm font-medium text-gray-700 mb-2">اسم الصلاحية <span class="text-red-500">*</span></label>
                    <input type="text" class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500" id="edit_name" name="name" required>
                </div>
                <div class="mb-4">
                    <label for="edit_guard_name" class="block text-sm font-medium text-gray-700 mb-2">الحارس <span class="text-red-500">*</span></label>
                    <select class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500" id="edit_guard_name" name="guard_name" required>
                        <option value="web">ويب</option>
                        <option value="api">API</option>
                        <option value="sanctum">Sanctum</option>
                    </select>
                </div>
            </div>
            <div class="flex justify-end space-x-2 pt-4">
                <button type="button" class="px-4 py-2 bg-gray-500 text-white text-sm font-medium rounded-md hover:bg-gray-600" onclick="closeModal('editPermissionModal')">إلغاء</button>
                <button type="submit" class="px-4 py-2 bg-blue-600 text-white text-sm font-medium rounded-md hover:bg-blue-700">تحديث</button>
            </div>
        </form>
    </div>
</div>

<!-- Bulk Create Modal -->
<div id="bulkCreateModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden z-50">
    <div class="relative top-20 mx-auto p-5 border w-11/12 max-w-2xl shadow-lg rounded-md bg-white">
        <div class="flex justify-between items-center pb-3">
            <h3 class="text-lg font-medium text-gray-900">إنشاء صلاحيات متعددة</h3>
            <button type="button" class="text-gray-400 hover:text-gray-600" onclick="closeModal('bulkCreateModal')">
                <i class="fas fa-times"></i>
            </button>
        </div>
        <form id="bulkCreateForm" action="<?php echo e(route('permissions.bulk-create')); ?>" method="POST">
            <?php echo csrf_field(); ?>
            <div class="mt-4">
                <div class="mb-4">
                    <label for="bulk_guard_name" class="block text-sm font-medium text-gray-700 mb-2">الحارس <span class="text-red-500">*</span></label>
                    <select class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500" id="bulk_guard_name" name="guard_name" required>
                        <option value="web">ويب</option>
                        <option value="api">API</option>
                        <option value="sanctum">Sanctum</option>
                    </select>
                </div>
                <div class="mb-4">
                    <label for="permissions_list" class="block text-sm font-medium text-gray-700 mb-2">قائمة الصلاحيات (واحدة في كل سطر) <span class="text-red-500">*</span></label>
                    <textarea class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500" id="permissions_list" rows="10" placeholder="users.create&#10;users.read&#10;users.update&#10;users.delete&#10;orders.create&#10;orders.read" required></textarea>
                    <p class="mt-1 text-sm text-gray-500">أدخل كل صلاحية في سطر منفصل</p>
                </div>
            </div>
            <div class="flex justify-end space-x-2 pt-4">
                <button type="button" class="px-4 py-2 bg-gray-500 text-white text-sm font-medium rounded-md hover:bg-gray-600" onclick="closeModal('bulkCreateModal')">إلغاء</button>
                <button type="submit" class="px-4 py-2 bg-blue-600 text-white text-sm font-medium rounded-md hover:bg-blue-700">إنشاء الصلاحيات</button>
            </div>
            </form>
        </div>
    </div>
</div>

<?php $__env->stopSection(); ?>

<?php $__env->startPush('scripts'); ?>
<!-- DataTables CDN -->
<link rel="stylesheet" type="text/css" href="https://cdn.datatables.net/1.11.5/css/dataTables.bootstrap4.min.css">
<script type="text/javascript" charset="utf8" src="https://cdn.datatables.net/1.11.5/js/jquery.dataTables.js"></script>
<script type="text/javascript" charset="utf8" src="https://cdn.datatables.net/1.11.5/js/dataTables.bootstrap4.min.js"></script>
<!-- Sweet Alert -->
<script src="<?php echo e(URL::asset('assets/plugins/sweet-alert/sweetalert.min.js')); ?>"></script>

<script>
// Modal Functions
function openModal(modalId) {
    document.getElementById(modalId).classList.remove('hidden');
}

function closeModal(modalId) {
    document.getElementById(modalId).classList.add('hidden');
}

$(document).ready(function() {
    // Initialize DataTable
    var table = $('#permissionsTable').DataTable({
        processing: true,
        serverSide: true,
        ajax: {
            url: "<?php echo e(route('permissions.data')); ?>",
            data: function (d) {
                d.group = $('#groupFilter').val();
                d.guard_name = $('#guardFilter').val();
            }
        },
        columns: [
            {data: 'DT_RowIndex', name: 'DT_RowIndex', orderable: false, searchable: false},
            {data: 'name', name: 'name'},
            {data: 'group', name: 'group'},
            {data: 'guard_badge', name: 'guard_name'},
            {data: 'roles_count', name: 'roles_count', orderable: false},
            {data: 'created_at', name: 'created_at'},
            {data: 'action', name: 'action', orderable: false, searchable: false}
        ],
        dom: 'Bfrtip',
        buttons: [
            'copy', 'csv', 'excel', 'pdf', 'print'
        ],
        language: {
            url: '//cdn.datatables.net/plug-ins/1.10.25/i18n/Arabic.json'
        }
    });

    // Handle Add Permission Form Submission
    $('#addPermissionForm').on('submit', function(e) {
        e.preventDefault();

        $.ajax({
            url: $(this).attr('action'),
            type: 'POST',
            data: $(this).serialize(),
            success: function(response) {
                closeModal('addPermissionModal');
                $('#addPermissionForm')[0].reset();
                swal("تم الحفظ!", "تم إضافة الصلاحية بنجاح.", "success");
                table.draw();
            },
            error: function(xhr) {
                var errors = xhr.responseJSON.errors;
                var errorMessage = 'حدث خطأ أثناء إضافة الصلاحية:\n';
                if (errors) {
                    Object.keys(errors).forEach(function(key) {
                        errorMessage += errors[key][0] + '\n';
                    });
                }
                swal("خطأ!", errorMessage, "error");
            }
        });
    });

    // Handle Edit Permission Form Submission
    $('#editPermissionForm').on('submit', function(e) {
        e.preventDefault();

        $.ajax({
            url: $(this).attr('action'),
            type: 'PUT',
            data: $(this).serialize(),
            success: function(response) {
                closeModal('editPermissionModal');
                swal("تم التحديث!", "تم تحديث الصلاحية بنجاح.", "success");
                table.draw();
            },
            error: function(xhr) {
                var errors = xhr.responseJSON.errors;
                var errorMessage = 'حدث خطأ أثناء تحديث الصلاحية:\n';
                if (errors) {
                    Object.keys(errors).forEach(function(key) {
                        errorMessage += errors[key][0] + '\n';
                    });
                }
                swal("خطأ!", errorMessage, "error");
            }
        });
    });

    // Handle Bulk Create Form Submission
    $('#bulkCreateForm').on('submit', function(e) {
        e.preventDefault();

        var permissions = $('#permissions_list').val().split('\n').filter(function(line) {
            return line.trim() !== '';
        });

        $.ajax({
            url: $(this).attr('action'),
            type: 'POST',
            data: {
                _token: $('input[name="_token"]').val(),
                guard_name: $('#bulk_guard_name').val(),
                permissions: permissions
            },
            success: function(response) {
                closeModal('bulkCreateModal');
                $('#bulkCreateForm')[0].reset();
                swal("تم الحفظ!", "تم إنشاء الصلاحيات بنجاح.", "success");
                table.draw();
            },
            error: function(xhr) {
                var errors = xhr.responseJSON.errors;
                var errorMessage = 'حدث خطأ أثناء إنشاء الصلاحيات:\n';
                if (errors) {
                    Object.keys(errors).forEach(function(key) {
                        errorMessage += errors[key][0] + '\n';
                    });
                }
                swal("خطأ!", errorMessage, "error");
            }
        });
    });

    // Export Functions
    $('#exportExcel').on('click', function() {
        table.button('.buttons-excel').trigger();
    });

    $('#exportPdf').on('click', function() {
        table.button('.buttons-pdf').trigger();
    });

    $('#printTable').on('click', function() {
        table.button('.buttons-print').trigger();
    });

    // Apply filters
    window.applyFilters = function() {
        table.draw();
    };

    // Clear filters
    window.clearFilters = function() {
        $('#groupFilter').val('');
        $('#guardFilter').val('');
        table.draw();
    };

    // Edit permission
    window.editPermission = function(id) {
        $.get("<?php echo e(url('admin/permissions')); ?>/" + id, function(data) {
            $('#edit_name').val(data.name);
            $('#edit_guard_name').val(data.guard_name);
            $('#editPermissionForm').attr('action', "<?php echo e(url('admin/permissions')); ?>/" + id);
            openModal('editPermissionModal');
        });
    };

    // Delete permission
    window.deletePermission = function(id) {
        swal({
            title: "هل أنت متأكد؟",
            text: "لن تتمكن من التراجع عن هذا الإجراء!",
            type: "warning",
            showCancelButton: true,
            confirmButtonColor: "#DD6B55",
            confirmButtonText: "نعم، احذف!",
            cancelButtonText: "إلغاء",
            closeOnConfirm: false,
            closeOnCancel: false
        }, function(isConfirm) {
            if (isConfirm) {
                $.ajax({
                    url: "<?php echo e(url('admin/permissions')); ?>/" + id,
                    type: 'DELETE',
                    data: {
                        _token: "<?php echo e(csrf_token()); ?>"
                    },
                    success: function(response) {
                        swal("تم الحذف!", "تم حذف الصلاحية بنجاح.", "success");
                        table.draw();
                    },
                    error: function(xhr) {
                        swal("خطأ!", "حدث خطأ أثناء حذف الصلاحية.", "error");
                    }
                });
            } else {
                swal("تم الإلغاء", "لم يتم حذف الصلاحية.", "error");
            }
        });
    };
});
</script>
<?php $__env->stopPush(); ?>
<?php echo $__env->make('layouts.master', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH D:\my dehive work\resturant-pos - Copy\epis - Copy\modules\Auth\Providers/../resources/views/permissions/index.blade.php ENDPATH**/ ?>