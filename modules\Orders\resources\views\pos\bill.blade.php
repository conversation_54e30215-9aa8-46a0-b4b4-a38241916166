<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Bill - Order #{{ $order->order_number }}</title>
    <style>
        body {
            font-family: 'Courier New', monospace;
            margin: 0;
            padding: 20px;
            font-size: 12px;
            line-height: 1.4;
        }
        .bill-container {
            max-width: 300px;
            margin: 0 auto;
            background: white;
        }
        .header {
            text-align: center;
            border-bottom: 2px solid #000;
            padding-bottom: 10px;
            margin-bottom: 15px;
        }
        .restaurant-name {
            font-size: 16px;
            font-weight: bold;
            margin-bottom: 5px;
        }
        .restaurant-info {
            font-size: 10px;
            margin-bottom: 2px;
        }
        .order-info {
            margin-bottom: 15px;
            border-bottom: 1px dashed #000;
            padding-bottom: 10px;
        }
        .order-info div {
            display: flex;
            justify-content: space-between;
            margin-bottom: 3px;
        }
        .items-section {
            margin-bottom: 15px;
        }
        .item {
            margin-bottom: 8px;
        }
        .item-header {
            display: flex;
            justify-content: space-between;
            font-weight: bold;
        }
        .item-details {
            font-size: 10px;
            color: #666;
            margin-left: 10px;
        }
        .totals-section {
            border-top: 1px dashed #000;
            padding-top: 10px;
            margin-bottom: 15px;
        }
        .total-line {
            display: flex;
            justify-content: space-between;
            margin-bottom: 3px;
        }
        .total-line.grand-total {
            font-weight: bold;
            font-size: 14px;
            border-top: 1px solid #000;
            padding-top: 5px;
            margin-top: 5px;
        }
        .footer {
            text-align: center;
            border-top: 2px solid #000;
            padding-top: 10px;
            font-size: 10px;
        }
        .payment-status {
            text-align: center;
            margin: 15px 0;
            padding: 8px;
            border: 2px solid #000;
            font-weight: bold;
        }
        .payment-status.due {
            background-color: #fff3cd;
            border-color: #ffc107;
        }
        .payment-status.paid {
            background-color: #d4edda;
            border-color: #28a745;
        }
        @media print {
            body { margin: 0; padding: 10px; }
            .bill-container { max-width: none; }
        }
    </style>
</head>
<body>
    <div class="bill-container">
        <!-- Header -->
        <div class="header">
            <div class="restaurant-name">{{ $order->branch->name ?? 'Restaurant' }}</div>
            @if($order->branch->address)
                <div class="restaurant-info">{{ $order->branch->address }}</div>
            @endif
            @if($order->branch->phone)
                <div class="restaurant-info">Tel: {{ $order->branch->phone }}</div>
            @endif
        </div>

        <!-- Order Information -->
        <div class="order-info">
            <div>
                <span>Order #:</span>
                <span>{{ $order->order_number }}</span>
            </div>
            <div>
                <span>Date:</span>
                <span>{{ $order->created_at->format('d/m/Y H:i') }}</span>
            </div>
            @if($order->table)
                <div>
                    <span>Table:</span>
                    <span>{{ $order->table->name }}</span>
                </div>
            @endif
            @if($order->customer)
                <div>
                    <span>Customer:</span>
                    <span>{{ $order->customer->name }}</span>
                </div>
            @endif
            <div>
                <span>Order Type:</span>
                <span>{{ ucfirst(str_replace('_', ' ', $order->order_type)) }}</span>
            </div>
            @if($order->pax)
                <div>
                    <span>Pax:</span>
                    <span>{{ $order->pax }}</span>
                </div>
            @endif
        </div>

        <!-- Items -->
        <div class="items-section">
            @foreach($order->orderItems as $item)
                <div class="item">
                    <div class="item-header">
                        <span>{{ $item->quantity }}x {{ $item->menuItem->name }}</span>
                        <span>${{ number_format($item->total_price, 2) }}</span>
                    </div>
                    @if($item->variant_name)
                        <div class="item-details">Variant: {{ $item->variant_name }}</div>
                    @endif
                    @if($item->notes)
                        <div class="item-details">Notes: {{ $item->notes }}</div>
                    @endif
                    @if($item->addons && count($item->addons) > 0)
                        @foreach($item->addons as $addon)
                            <div class="item-details">+ {{ $addon['addon_name'] }} ({{ $addon['quantity'] }}x) - ${{ number_format($addon['total_price'], 2) }}</div>
                        @endforeach
                    @endif
                </div>
            @endforeach
        </div>

        <!-- Totals -->
        <div class="totals-section">
            <div class="total-line">
                <span>Subtotal:</span>
                <span>${{ number_format($order->subtotal, 2) }}</span>
            </div>
            @if($order->discount_amount > 0)
                <div class="total-line">
                    <span>Discount:</span>
                    <span>-${{ number_format($order->discount_amount, 2) }}</span>
                </div>
            @endif
            @if($order->tax_amount > 0)
                <div class="total-line">
                    <span>Tax (10%):</span>
                    <span>${{ number_format($order->tax_amount, 2) }}</span>
                </div>
            @endif
            @if($order->service_charge > 0)
                <div class="total-line">
                    <span>Service Charge:</span>
                    <span>${{ number_format($order->service_charge, 2) }}</span>
                </div>
            @endif
            <div class="total-line grand-total">
                <span>TOTAL:</span>
                <span>${{ number_format($order->total_amount, 2) }}</span>
            </div>
        </div>

        <!-- Payment Status -->
        @if($order->transaction)
            <div class="payment-status {{ $order->transaction->status === 'paid' ? 'paid' : 'due' }}">
                @if($order->transaction->status === 'paid')
                    PAID
                @elseif($order->transaction->status === 'partially_paid')
                    PARTIALLY PAID - Due: ${{ number_format($order->transaction->due_amount, 2) }}
                @else
                    AMOUNT DUE: ${{ number_format($order->transaction->due_amount, 2) }}
                @endif
            </div>
        @endif

        @if($order->notes)
            <div style="margin: 15px 0; padding: 8px; border: 1px solid #ddd;">
                <strong>Notes:</strong> {{ $order->notes }}
            </div>
        @endif

        <!-- Footer -->
        <div class="footer">
            <div>Thank you for your visit!</div>
            <div>{{ now()->format('d/m/Y H:i:s') }}</div>
        </div>
    </div>

    <script>
        // Auto print when loaded
        window.onload = function() {
            window.print();
        }
    </script>
</body>
</html>
